<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg
PUBLIC "-//W3C//DTD SVG 1.1//EN"
     "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
     <!--Created with SF Symbol Generator (v1.0.0)-->

<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="3300" height="2200">
<!--glyph: "keyboard-cable.medium", point size: 100.0-->
<style>.SFSymbolsPreviewWireframe {fill:none;opacity:1.0;stroke:black;stroke-width:0.5}
</style>
<g id="Notes">
<rect height="2200" id="artboard" style="fill:white;opacity:1" width="3300" x="0" y="0"/>
<line style="fill:none;stroke:black;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="292" y2="292"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 263 322)">Weight/Scale Variations</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 559.711 322)">Ultralight</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 856.422 322)">Thin</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1153.13 322)">Light</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1449.84 322)">Regular</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1746.56 322)">Medium</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2043.27 322)">Semibold</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2339.98 322)">Bold</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2636.69 322)">Heavy</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2933.4 322)">Black</text>
<line style="fill:none;stroke:black;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1903" y2="1903"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 263 1953)">Design Variations</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1971)">Symbols are supported in up to nine weights and three scales.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1989)">For optimal layout with text and other symbols, vertically align</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 2007)">symbols with the adjacent text.</text>
<line style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="776" x2="776" y1="1919" y2="1933"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 776 1953)">Margins</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 1971)">Leading and trailing margins on the left and right side of each symbol</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 1989)">can be adjusted by modifying the x-location of the margin guidelines.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 2007)">Modifications are automatically applied proportionally to all</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 2025)">scales and weights.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 1289 1953)">Exporting</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 1289 1971)">Symbols should be outlined when exporting to ensure the</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 1289 1989)">design is preserved when submitting to Xcode.</text>
<text id="template-version" style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1933)">Template v.5.0</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1951)">Requires Xcode 15 or greater</text>
<text id="descriptive-name" style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1969)">Generated from keyboard-cable</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1987)">Typeset at 100.0 points</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 726)">Small</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1156)">Medium</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1586)">Large</text>
</g>
<g id="Guides">
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 696)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-S" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="696" y2="696"/>
<line id="Capline-S" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="625.541" y2="625.541"/>
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 1126)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-M" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1126" y2="1126"/>
<line id="Capline-M" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1055.54" y2="1055.54"/>
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 1556)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-L" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1556" y2="1556"/>
<line id="Capline-L" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1485.54" y2="1485.54"/>
<line id="left-margin-Ultralight-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="513.711" x2="513.711" y1="600.785" y2="720.121"/>
<line id="right-margin-Ultralight-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="605.711" x2="605.711" y1="600.785" y2="720.121"/>
<line id="left-margin-Regular-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="1403.84" x2="1403.84" y1="600.785" y2="720.121"/>
<line id="right-margin-Regular-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="1495.84" x2="1495.84" y1="600.785" y2="720.121"/>
<line id="left-margin-Black-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="2887.4" x2="2887.4" y1="600.785" y2="720.121"/>
<line id="right-margin-Black-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="2979.4" x2="2979.4" y1="600.785" y2="720.121"/>
</g>
<g id="Symbols">
<g id="Ultralight-S" transform="matrix(1 0 0 1 513.711 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="keyboard-cable">
<g id="Union">
<path d="M22.0417 62.2917C24.1588 62.2917 25.875 64.008 25.875 66.125C25.875 68.242 24.1588 69.9583 22.0417 69.9583C19.9246 69.9583 18.2083 68.242 18.2083 66.125C18.2083 64.008 19.9246 62.2917 22.0417 62.2917Z" fill="black"/>
<path d="M69.9583 62.2917C72.0754 62.2917 73.7917 64.008 73.7917 66.125C73.7917 68.242 72.0754 69.9583 69.9583 69.9583C67.8421 69.9578 66.125 68.2419 66.125 66.125C66.125 64.0081 67.8421 62.2922 69.9583 62.2917Z" fill="black"/>
<path d="M54.625 63.25C56.2128 63.25 57.5 64.5372 57.5 66.125C57.5 67.7128 56.2128 69 54.625 69H37.375C35.7872 69 34.5 67.7128 34.5 66.125C34.5 64.5372 35.7872 63.25 37.375 63.25H54.625Z" fill="black"/>
<path d="M22.0417 45.0417C24.1588 45.0417 25.875 46.758 25.875 48.875C25.875 50.992 24.1588 52.7083 22.0417 52.7083C19.9246 52.7083 18.2083 50.992 18.2083 48.875C18.2083 46.758 19.9246 45.0417 22.0417 45.0417Z" fill="black"/>
<path d="M37.375 45.0417C39.492 45.0417 41.2083 46.758 41.2083 48.875C41.2083 50.992 39.492 52.7083 37.375 52.7083C35.2579 52.7083 33.5417 50.992 33.5417 48.875C33.5417 46.758 35.2579 45.0417 37.375 45.0417Z" fill="black"/>
<path d="M54.625 45.0417C56.742 45.0417 58.4583 46.758 58.4583 48.875C58.4583 50.992 56.742 52.7083 54.625 52.7083C52.508 52.7083 50.7917 50.992 50.7917 48.875C50.7917 46.758 52.508 45.0417 54.625 45.0417Z" fill="black"/>
<path d="M69.9583 45.0417C72.0754 45.0417 73.7917 46.758 73.7917 48.875C73.7917 50.992 72.0754 52.7083 69.9583 52.7083C67.8421 52.7078 66.125 50.9919 66.125 48.875C66.125 46.7581 67.8421 45.0422 69.9583 45.0417Z" fill="black"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M69.9583 7.66667C71.5462 7.66667 72.8333 8.95385 72.8333 10.5417V12.4583C72.8333 17.2218 68.9717 21.0833 64.2083 21.0833H31.625C27.9201 21.0833 24.9167 24.0868 24.9167 27.7917V30.6667H73.7917C81.7309 30.6667 88.1667 37.1024 88.1667 45.0417V69.9583C88.1667 77.8976 81.7309 84.3333 73.7917 84.3333H18.2083C10.2693 84.3333 3.83333 77.8976 3.83333 69.9583V45.0417C3.83333 37.1024 10.2693 30.6667 18.2083 30.6667H19.1667V27.7917C19.1667 20.9111 24.7445 15.3333 31.625 15.3333H64.2083C65.7961 15.3333 67.0833 14.0461 67.0833 12.4583V10.5417C67.0833 8.95385 68.3705 7.66667 69.9583 7.66667ZM18.2083 36.4167C13.4449 36.4167 9.58333 40.2781 9.58333 45.0417V69.9583C9.58333 74.7219 13.4449 78.5833 18.2083 78.5833H73.7917C78.5553 78.5833 82.4167 74.722 82.4167 69.9583V45.0417C82.4167 40.278 78.5553 36.4167 73.7917 36.4167H18.2083Z" fill="black"/>
</g>
</g>
</svg>

</g>
<g id="Regular-S" transform="matrix(1 0 0 1 1403.84 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="keyboard-cable">
<g id="Union">
<path d="M22.0417 62.2917C24.1588 62.2917 25.875 64.008 25.875 66.125C25.875 68.242 24.1588 69.9583 22.0417 69.9583C19.9246 69.9583 18.2083 68.242 18.2083 66.125C18.2083 64.008 19.9246 62.2917 22.0417 62.2917Z" fill="black"/>
<path d="M69.9583 62.2917C72.0754 62.2917 73.7917 64.008 73.7917 66.125C73.7917 68.242 72.0754 69.9583 69.9583 69.9583C67.8421 69.9578 66.125 68.2419 66.125 66.125C66.125 64.0081 67.8421 62.2922 69.9583 62.2917Z" fill="black"/>
<path d="M54.625 63.25C56.2128 63.25 57.5 64.5372 57.5 66.125C57.5 67.7128 56.2128 69 54.625 69H37.375C35.7872 69 34.5 67.7128 34.5 66.125C34.5 64.5372 35.7872 63.25 37.375 63.25H54.625Z" fill="black"/>
<path d="M22.0417 45.0417C24.1588 45.0417 25.875 46.758 25.875 48.875C25.875 50.992 24.1588 52.7083 22.0417 52.7083C19.9246 52.7083 18.2083 50.992 18.2083 48.875C18.2083 46.758 19.9246 45.0417 22.0417 45.0417Z" fill="black"/>
<path d="M37.375 45.0417C39.492 45.0417 41.2083 46.758 41.2083 48.875C41.2083 50.992 39.492 52.7083 37.375 52.7083C35.2579 52.7083 33.5417 50.992 33.5417 48.875C33.5417 46.758 35.2579 45.0417 37.375 45.0417Z" fill="black"/>
<path d="M54.625 45.0417C56.742 45.0417 58.4583 46.758 58.4583 48.875C58.4583 50.992 56.742 52.7083 54.625 52.7083C52.508 52.7083 50.7917 50.992 50.7917 48.875C50.7917 46.758 52.508 45.0417 54.625 45.0417Z" fill="black"/>
<path d="M69.9583 45.0417C72.0754 45.0417 73.7917 46.758 73.7917 48.875C73.7917 50.992 72.0754 52.7083 69.9583 52.7083C67.8421 52.7078 66.125 50.9919 66.125 48.875C66.125 46.7581 67.8421 45.0422 69.9583 45.0417Z" fill="black"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M69.9583 7.66667C71.5462 7.66667 72.8333 8.95385 72.8333 10.5417V12.4583C72.8333 17.2218 68.9717 21.0833 64.2083 21.0833H31.625C27.9201 21.0833 24.9167 24.0868 24.9167 27.7917V30.6667H73.7917C81.7309 30.6667 88.1667 37.1024 88.1667 45.0417V69.9583C88.1667 77.8976 81.7309 84.3333 73.7917 84.3333H18.2083C10.2693 84.3333 3.83333 77.8976 3.83333 69.9583V45.0417C3.83333 37.1024 10.2693 30.6667 18.2083 30.6667H19.1667V27.7917C19.1667 20.9111 24.7445 15.3333 31.625 15.3333H64.2083C65.7961 15.3333 67.0833 14.0461 67.0833 12.4583V10.5417C67.0833 8.95385 68.3705 7.66667 69.9583 7.66667ZM18.2083 36.4167C13.4449 36.4167 9.58333 40.2781 9.58333 45.0417V69.9583C9.58333 74.7219 13.4449 78.5833 18.2083 78.5833H73.7917C78.5553 78.5833 82.4167 74.722 82.4167 69.9583V45.0417C82.4167 40.278 78.5553 36.4167 73.7917 36.4167H18.2083Z" fill="black"/>
</g>
</g>
</svg>

</g>
<g id="Black-S" transform="matrix(1 0 0 1 2887.4 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="keyboard-cable">
<g id="Union">
<path d="M22.0417 62.2917C24.1588 62.2917 25.875 64.008 25.875 66.125C25.875 68.242 24.1588 69.9583 22.0417 69.9583C19.9246 69.9583 18.2083 68.242 18.2083 66.125C18.2083 64.008 19.9246 62.2917 22.0417 62.2917Z" fill="black"/>
<path d="M69.9583 62.2917C72.0754 62.2917 73.7917 64.008 73.7917 66.125C73.7917 68.242 72.0754 69.9583 69.9583 69.9583C67.8421 69.9578 66.125 68.2419 66.125 66.125C66.125 64.0081 67.8421 62.2922 69.9583 62.2917Z" fill="black"/>
<path d="M54.625 63.25C56.2128 63.25 57.5 64.5372 57.5 66.125C57.5 67.7128 56.2128 69 54.625 69H37.375C35.7872 69 34.5 67.7128 34.5 66.125C34.5 64.5372 35.7872 63.25 37.375 63.25H54.625Z" fill="black"/>
<path d="M22.0417 45.0417C24.1588 45.0417 25.875 46.758 25.875 48.875C25.875 50.992 24.1588 52.7083 22.0417 52.7083C19.9246 52.7083 18.2083 50.992 18.2083 48.875C18.2083 46.758 19.9246 45.0417 22.0417 45.0417Z" fill="black"/>
<path d="M37.375 45.0417C39.492 45.0417 41.2083 46.758 41.2083 48.875C41.2083 50.992 39.492 52.7083 37.375 52.7083C35.2579 52.7083 33.5417 50.992 33.5417 48.875C33.5417 46.758 35.2579 45.0417 37.375 45.0417Z" fill="black"/>
<path d="M54.625 45.0417C56.742 45.0417 58.4583 46.758 58.4583 48.875C58.4583 50.992 56.742 52.7083 54.625 52.7083C52.508 52.7083 50.7917 50.992 50.7917 48.875C50.7917 46.758 52.508 45.0417 54.625 45.0417Z" fill="black"/>
<path d="M69.9583 45.0417C72.0754 45.0417 73.7917 46.758 73.7917 48.875C73.7917 50.992 72.0754 52.7083 69.9583 52.7083C67.8421 52.7078 66.125 50.9919 66.125 48.875C66.125 46.7581 67.8421 45.0422 69.9583 45.0417Z" fill="black"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M69.9583 7.66667C71.5462 7.66667 72.8333 8.95385 72.8333 10.5417V12.4583C72.8333 17.2218 68.9717 21.0833 64.2083 21.0833H31.625C27.9201 21.0833 24.9167 24.0868 24.9167 27.7917V30.6667H73.7917C81.7309 30.6667 88.1667 37.1024 88.1667 45.0417V69.9583C88.1667 77.8976 81.7309 84.3333 73.7917 84.3333H18.2083C10.2693 84.3333 3.83333 77.8976 3.83333 69.9583V45.0417C3.83333 37.1024 10.2693 30.6667 18.2083 30.6667H19.1667V27.7917C19.1667 20.9111 24.7445 15.3333 31.625 15.3333H64.2083C65.7961 15.3333 67.0833 14.0461 67.0833 12.4583V10.5417C67.0833 8.95385 68.3705 7.66667 69.9583 7.66667ZM18.2083 36.4167C13.4449 36.4167 9.58333 40.2781 9.58333 45.0417V69.9583C9.58333 74.7219 13.4449 78.5833 18.2083 78.5833H73.7917C78.5553 78.5833 82.4167 74.722 82.4167 69.9583V45.0417C82.4167 40.278 78.5553 36.4167 73.7917 36.4167H18.2083Z" fill="black"/>
</g>
</g>
</svg>

</g>
</g>
</svg>