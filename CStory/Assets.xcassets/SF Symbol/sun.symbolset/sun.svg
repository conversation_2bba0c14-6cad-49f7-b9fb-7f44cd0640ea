<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg
PUBLIC "-//W3C//DTD SVG 1.1//EN"
     "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
     <!--Created with SF Symbol Generator (v1.0.0)-->

<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="3300" height="2200">
<!--glyph: "sun.medium", point size: 100.0-->
<style>.SFSymbolsPreviewWireframe {fill:none;opacity:1.0;stroke:black;stroke-width:0.5}
</style>
<g id="Notes">
<rect height="2200" id="artboard" style="fill:white;opacity:1" width="3300" x="0" y="0"/>
<line style="fill:none;stroke:black;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="292" y2="292"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 263 322)">Weight/Scale Variations</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 559.711 322)">Ultralight</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 856.422 322)">Thin</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1153.13 322)">Light</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1449.84 322)">Regular</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1746.56 322)">Medium</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2043.27 322)">Semibold</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2339.98 322)">Bold</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2636.69 322)">Heavy</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2933.4 322)">Black</text>
<line style="fill:none;stroke:black;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1903" y2="1903"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 263 1953)">Design Variations</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1971)">Symbols are supported in up to nine weights and three scales.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1989)">For optimal layout with text and other symbols, vertically align</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 2007)">symbols with the adjacent text.</text>
<line style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="776" x2="776" y1="1919" y2="1933"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 776 1953)">Margins</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 1971)">Leading and trailing margins on the left and right side of each symbol</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 1989)">can be adjusted by modifying the x-location of the margin guidelines.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 2007)">Modifications are automatically applied proportionally to all</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 2025)">scales and weights.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 1289 1953)">Exporting</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 1289 1971)">Symbols should be outlined when exporting to ensure the</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 1289 1989)">design is preserved when submitting to Xcode.</text>
<text id="template-version" style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1933)">Template v.5.0</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1951)">Requires Xcode 15 or greater</text>
<text id="descriptive-name" style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1969)">Generated from sun</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1987)">Typeset at 100.0 points</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 726)">Small</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1156)">Medium</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1586)">Large</text>
</g>
<g id="Guides">
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 696)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-S" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="696" y2="696"/>
<line id="Capline-S" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="625.541" y2="625.541"/>
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 1126)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-M" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1126" y2="1126"/>
<line id="Capline-M" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1055.54" y2="1055.54"/>
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 1556)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-L" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1556" y2="1556"/>
<line id="Capline-L" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1485.54" y2="1485.54"/>
<line id="left-margin-Ultralight-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="513.711" x2="513.711" y1="600.785" y2="720.121"/>
<line id="right-margin-Ultralight-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="605.711" x2="605.711" y1="600.785" y2="720.121"/>
<line id="left-margin-Regular-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="1403.84" x2="1403.84" y1="600.785" y2="720.121"/>
<line id="right-margin-Regular-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="1495.84" x2="1495.84" y1="600.785" y2="720.121"/>
<line id="left-margin-Black-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="2887.4" x2="2887.4" y1="600.785" y2="720.121"/>
<line id="right-margin-Black-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="2979.4" x2="2979.4" y1="600.785" y2="720.121"/>
</g>
<g id="Symbols">
<g id="Ultralight-S" transform="matrix(1 0 0 1 513.711 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="sun">
<g id="Union">
<path d="M45.9925 76.5094C47.5798 76.5094 48.8667 77.7974 48.8675 79.3844V85.2243C48.8675 86.8121 47.5803 88.0993 45.9925 88.0993C44.405 88.099 43.1175 86.8119 43.1175 85.2243V79.3844C43.1184 77.7976 44.4055 76.5098 45.9925 76.5094Z" fill="black"/>
<path d="M20.3534 67.5737C21.4759 66.452 23.2962 66.452 24.4188 67.5737C25.5413 68.6962 25.5408 70.5163 24.4188 71.6392L20.2897 75.7682C19.167 76.891 17.347 76.891 16.2243 75.7682C15.1023 74.6454 15.1018 72.8253 16.2243 71.7028L20.3534 67.5737Z" fill="black"/>
<path d="M67.5662 67.5737C68.6888 66.4521 70.5091 66.4519 71.6317 67.5737L75.7607 71.7028C76.8834 72.8253 76.8829 74.6454 75.7607 75.7682C74.638 76.891 72.8181 76.8909 71.6953 75.7682L67.5662 71.6392C66.4442 70.5164 66.4438 68.6963 67.5662 67.5737Z" fill="black"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M29.7308 29.7383C38.7129 20.7564 53.2758 20.7563 62.258 29.7383C71.2382 38.7205 71.2393 53.2841 62.258 62.2655C53.2763 71.2471 38.713 71.246 29.7308 62.2655C20.7486 53.2836 20.7488 38.7205 29.7308 29.7383ZM58.1888 33.8037C51.4521 27.0675 40.5327 27.0675 33.7962 33.8037C27.0597 40.5404 27.0599 51.46 33.7962 58.1963C40.5328 64.933 51.4521 64.933 58.1888 58.1963C64.925 51.46 64.9252 40.5404 58.1888 33.8037Z" fill="black"/>
<path d="M12.6081 43.125C14.1955 43.1254 15.4829 44.4126 15.4831 46C15.4831 47.5876 14.1956 48.8746 12.6081 48.875H6.76823C5.18041 48.875 3.89323 47.5878 3.89323 46C3.89343 44.4124 5.18054 43.125 6.76823 43.125H12.6081Z" fill="black"/>
<path d="M85.2168 43.125C86.8042 43.1253 88.0916 44.4126 88.0918 46C88.0918 47.5876 86.8043 48.8747 85.2168 48.875H79.377C77.7891 48.875 76.502 47.5878 76.502 46C76.5022 44.4124 77.7893 43.125 79.377 43.125H85.2168Z" fill="black"/>
<path d="M16.2243 16.2318C17.347 15.109 19.167 15.109 20.2897 16.2318L24.4188 20.3608C25.5413 21.4836 25.5415 23.3036 24.4188 24.4263C23.2961 25.5489 21.4761 25.5488 20.3534 24.4263L16.2243 20.2972C15.1015 19.1744 15.1016 17.3545 16.2243 16.2318Z" fill="black"/>
<path d="M71.6953 16.2318C72.8181 15.1091 74.638 15.1091 75.7607 16.2318C76.8834 17.3545 76.8834 19.1745 75.7607 20.2972L71.6317 24.4263C70.5089 25.5489 68.689 25.5489 67.5662 24.4263C66.4435 23.3035 66.4435 21.4836 67.5662 20.3608L71.6953 16.2318Z" fill="black"/>
<path d="M45.9925 3.90072C47.5803 3.90072 48.8675 5.1879 48.8675 6.77572V12.6156C48.8671 14.203 47.5801 15.4906 45.9925 15.4906C44.4052 15.4902 43.1179 14.2028 43.1175 12.6156V6.77572C43.1175 5.1881 44.405 3.90104 45.9925 3.90072Z" fill="black"/>
</g>
</g>
</svg>

</g>
<g id="Regular-S" transform="matrix(1 0 0 1 1403.84 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="sun">
<g id="Union">
<path d="M45.9925 76.5094C47.5798 76.5094 48.8667 77.7974 48.8675 79.3844V85.2243C48.8675 86.8121 47.5803 88.0993 45.9925 88.0993C44.405 88.099 43.1175 86.8119 43.1175 85.2243V79.3844C43.1184 77.7976 44.4055 76.5098 45.9925 76.5094Z" fill="black"/>
<path d="M20.3534 67.5737C21.4759 66.452 23.2962 66.452 24.4188 67.5737C25.5413 68.6962 25.5408 70.5163 24.4188 71.6392L20.2897 75.7682C19.167 76.891 17.347 76.891 16.2243 75.7682C15.1023 74.6454 15.1018 72.8253 16.2243 71.7028L20.3534 67.5737Z" fill="black"/>
<path d="M67.5662 67.5737C68.6888 66.4521 70.5091 66.4519 71.6317 67.5737L75.7607 71.7028C76.8834 72.8253 76.8829 74.6454 75.7607 75.7682C74.638 76.891 72.8181 76.8909 71.6953 75.7682L67.5662 71.6392C66.4442 70.5164 66.4438 68.6963 67.5662 67.5737Z" fill="black"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M29.7308 29.7383C38.7129 20.7564 53.2758 20.7563 62.258 29.7383C71.2382 38.7205 71.2393 53.2841 62.258 62.2655C53.2763 71.2471 38.713 71.246 29.7308 62.2655C20.7486 53.2836 20.7488 38.7205 29.7308 29.7383ZM58.1888 33.8037C51.4521 27.0675 40.5327 27.0675 33.7962 33.8037C27.0597 40.5404 27.0599 51.46 33.7962 58.1963C40.5328 64.933 51.4521 64.933 58.1888 58.1963C64.925 51.46 64.9252 40.5404 58.1888 33.8037Z" fill="black"/>
<path d="M12.6081 43.125C14.1955 43.1254 15.4829 44.4126 15.4831 46C15.4831 47.5876 14.1956 48.8746 12.6081 48.875H6.76823C5.18041 48.875 3.89323 47.5878 3.89323 46C3.89343 44.4124 5.18054 43.125 6.76823 43.125H12.6081Z" fill="black"/>
<path d="M85.2168 43.125C86.8042 43.1253 88.0916 44.4126 88.0918 46C88.0918 47.5876 86.8043 48.8747 85.2168 48.875H79.377C77.7891 48.875 76.502 47.5878 76.502 46C76.5022 44.4124 77.7893 43.125 79.377 43.125H85.2168Z" fill="black"/>
<path d="M16.2243 16.2318C17.347 15.109 19.167 15.109 20.2897 16.2318L24.4188 20.3608C25.5413 21.4836 25.5415 23.3036 24.4188 24.4263C23.2961 25.5489 21.4761 25.5488 20.3534 24.4263L16.2243 20.2972C15.1015 19.1744 15.1016 17.3545 16.2243 16.2318Z" fill="black"/>
<path d="M71.6953 16.2318C72.8181 15.1091 74.638 15.1091 75.7607 16.2318C76.8834 17.3545 76.8834 19.1745 75.7607 20.2972L71.6317 24.4263C70.5089 25.5489 68.689 25.5489 67.5662 24.4263C66.4435 23.3035 66.4435 21.4836 67.5662 20.3608L71.6953 16.2318Z" fill="black"/>
<path d="M45.9925 3.90072C47.5803 3.90072 48.8675 5.1879 48.8675 6.77572V12.6156C48.8671 14.203 47.5801 15.4906 45.9925 15.4906C44.4052 15.4902 43.1179 14.2028 43.1175 12.6156V6.77572C43.1175 5.1881 44.405 3.90104 45.9925 3.90072Z" fill="black"/>
</g>
</g>
</svg>

</g>
<g id="Black-S" transform="matrix(1 0 0 1 2887.4 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="sun">
<g id="Union">
<path d="M45.9925 76.5094C47.5798 76.5094 48.8667 77.7974 48.8675 79.3844V85.2243C48.8675 86.8121 47.5803 88.0993 45.9925 88.0993C44.405 88.099 43.1175 86.8119 43.1175 85.2243V79.3844C43.1184 77.7976 44.4055 76.5098 45.9925 76.5094Z" fill="black"/>
<path d="M20.3534 67.5737C21.4759 66.452 23.2962 66.452 24.4188 67.5737C25.5413 68.6962 25.5408 70.5163 24.4188 71.6392L20.2897 75.7682C19.167 76.891 17.347 76.891 16.2243 75.7682C15.1023 74.6454 15.1018 72.8253 16.2243 71.7028L20.3534 67.5737Z" fill="black"/>
<path d="M67.5662 67.5737C68.6888 66.4521 70.5091 66.4519 71.6317 67.5737L75.7607 71.7028C76.8834 72.8253 76.8829 74.6454 75.7607 75.7682C74.638 76.891 72.8181 76.8909 71.6953 75.7682L67.5662 71.6392C66.4442 70.5164 66.4438 68.6963 67.5662 67.5737Z" fill="black"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M29.7308 29.7383C38.7129 20.7564 53.2758 20.7563 62.258 29.7383C71.2382 38.7205 71.2393 53.2841 62.258 62.2655C53.2763 71.2471 38.713 71.246 29.7308 62.2655C20.7486 53.2836 20.7488 38.7205 29.7308 29.7383ZM58.1888 33.8037C51.4521 27.0675 40.5327 27.0675 33.7962 33.8037C27.0597 40.5404 27.0599 51.46 33.7962 58.1963C40.5328 64.933 51.4521 64.933 58.1888 58.1963C64.925 51.46 64.9252 40.5404 58.1888 33.8037Z" fill="black"/>
<path d="M12.6081 43.125C14.1955 43.1254 15.4829 44.4126 15.4831 46C15.4831 47.5876 14.1956 48.8746 12.6081 48.875H6.76823C5.18041 48.875 3.89323 47.5878 3.89323 46C3.89343 44.4124 5.18054 43.125 6.76823 43.125H12.6081Z" fill="black"/>
<path d="M85.2168 43.125C86.8042 43.1253 88.0916 44.4126 88.0918 46C88.0918 47.5876 86.8043 48.8747 85.2168 48.875H79.377C77.7891 48.875 76.502 47.5878 76.502 46C76.5022 44.4124 77.7893 43.125 79.377 43.125H85.2168Z" fill="black"/>
<path d="M16.2243 16.2318C17.347 15.109 19.167 15.109 20.2897 16.2318L24.4188 20.3608C25.5413 21.4836 25.5415 23.3036 24.4188 24.4263C23.2961 25.5489 21.4761 25.5488 20.3534 24.4263L16.2243 20.2972C15.1015 19.1744 15.1016 17.3545 16.2243 16.2318Z" fill="black"/>
<path d="M71.6953 16.2318C72.8181 15.1091 74.638 15.1091 75.7607 16.2318C76.8834 17.3545 76.8834 19.1745 75.7607 20.2972L71.6317 24.4263C70.5089 25.5489 68.689 25.5489 67.5662 24.4263C66.4435 23.3035 66.4435 21.4836 67.5662 20.3608L71.6953 16.2318Z" fill="black"/>
<path d="M45.9925 3.90072C47.5803 3.90072 48.8675 5.1879 48.8675 6.77572V12.6156C48.8671 14.203 47.5801 15.4906 45.9925 15.4906C44.4052 15.4902 43.1179 14.2028 43.1175 12.6156V6.77572C43.1175 5.1881 44.405 3.90104 45.9925 3.90072Z" fill="black"/>
</g>
</g>
</svg>

</g>
</g>
</svg>