//
//  CategoryFormView.swift
//  CStory
//
//  Created by NZUE on 2025/12/19.
//

import AVFoundation
import ImageIO
import MobileCoreServices
// 导入自定义图片选择器
import PhotosUI
import SwiftData
import SwiftUI

/// 类别表单视图模式
enum CategoryFormMode {
  case create(isMainCategory: Bool, mainCategoryId: String?, selectedType: TransactionType)
  case edit(categoryId: String, isMainCategory: Bool)

  var isEditing: Bool {
    switch self {
    case .create: return false
    case .edit: return true
    }
  }

  var title: String {
    switch self {
    case .create(let isMainCategory, _, _):
      return "创建\(isMainCategory ? "主" : "子")类别"
    case .edit(_, let isMainCategory):
      return "编辑\(isMainCategory ? "主" : "子")类别"
    }
  }

  var isMainCategory: Bool {
    switch self {
    case .create(let isMainCategory, _, _):
      return isMainCategory
    case .edit(_, let isMainCategory):
      return isMainCategory
    }
  }
}

struct CategoryFormView: View {
  @Environment(\.dismiss) private var dismiss
  @Environment(\.modelContext) private var modelContext
  @Environment(\.dataManager) private var dataManager
  @EnvironmentObject private var pathManager: PathManagerHelper

  // MARK: - ViewModel
  @ObservedObject private var viewModel: CategoryFormVM

  // MARK: - 配置
  let mode: CategoryFormMode

  // MARK: - 初始化
  init(viewModel: CategoryFormVM, mode: CategoryFormMode) {
    self._viewModel = ObservedObject(wrappedValue: viewModel)
    self.mode = mode
  }

  var body: some View {
    ZStack {
      VStack(spacing: 0) {
        // 导航栏

        NavigationBarKit(
          title: mode.title,
          onBack: {
            dismiss()
          }
        )
        // 主要内容
        ScrollView(showsIndicators: false) {
          LazyVStack(spacing: 12) {
            // 图标选择区域
            VStack(alignment: .leading, spacing: 8) {
              HStack(spacing: 12) {
                // 图标预览
                IconView(
                  viewModel: IconViewVM(
                    icon: viewModel.icon,
                    size: 56,
                    fontSize: 40,
                    backgroundColor: .clear,
                    cornerRadius: 16
                  )
                )

                TextField("请输入类别名称", text: $viewModel.name)
                  .font(.system(size: 16))
                  .foregroundColor(.cBlack)
                  .padding(.horizontal, 16)
                  .frame(height: 56)
                  .background(.cWhite.opacity(0.5))
                  .cornerRadius(24)
                  .overlay(
                    RoundedRectangle(cornerRadius: 24)
                      .strokeBorder(
                        viewModel.isNameEmpty && !viewModel.name.isEmpty
                          ? .cAccentRed.opacity(0.5) : .cAccentBlue.opacity(0.08),
                        lineWidth: 1
                      )
                  )
              }

              if viewModel.isNameEmpty && !viewModel.name.isEmpty {
                Text("类别名称不能为空")
                  .font(.system(size: 13, weight: .regular))
                  .foregroundColor(.cAccentRed)
                  .padding(.leading, 68)  // 68 = 56 (icon size) + 12 (spacing)
              }
            }

            // 图标输入区域（包含类型切换）
            iconInputSection
            Spacer()

          }
          .padding(.horizontal, 16)
          .padding(.top, 12)
          .padding(.bottom, 80)  // 增加底部间距，避免被悬浮按钮遮挡
        }
      }

      // 底部操作按钮区域（悬浮层）
      actionButtonSection
    }
    .background(.cLightBlue)
    .navigationBarTitleDisplayMode(.inline)
    .navigationBarBackButtonHidden(true)
    .onAppear {
      viewModel.completeInitialLoad()
    }
    .onChange(of: viewModel.name) { _, _ in
      viewModel.handleNameChange()
    }
    .onChange(of: viewModel.selectedImage) { _, _ in
      viewModel.handleSelectedImageChange()
    }
    .onChange(of: viewModel.croppedImage) { _, _ in
      viewModel.handleCroppedImageChange()
    }
    .onChange(of: viewModel.emojiInput) { _, _ in
      viewModel.handleEmojiInputChange()
    }
    // 使用自定义的图片选择器，支持图片裁剪
    .cropImagePicker(show: $viewModel.showImagePicker, croppedImage: $viewModel.croppedImage)
  }

  // MARK: - 视图组件

  /// 底部操作按钮区域
  private var actionButtonSection: some View {
    FloatingActionButtonView(
      buttons: [
        FloatingActionButton(
          title: "取消",
          action: {
            viewModel.triggerFeedback.toggle()
            dismiss()
          },
          style: .secondary
        ),
        FloatingActionButton(
          title: "保存",
          action: {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
              if viewModel.handleSave() {
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.6) {
                  dismiss()
                }
              }
            }
          },
          style: .primary,
          isEnabled: viewModel.canSave
        ),
      ]
    )
  }

  /// 图标输入区域（包含类型切换）
  private var iconInputSection: some View {
    HStack(spacing: 0) {
      // 统一的输入区域
      if viewModel.selectedIconMode == .emoji {
        // Emoji 输入模式 - 整个区域可点击进入输入状态
        TextField("请输入Emoji符号", text: $viewModel.emojiInput)
          .font(.system(size: 16))
          .foregroundColor(.cBlack)
          .padding(.horizontal, 16)
          .frame(maxWidth: .infinity, minHeight: 56)
      } else {
        // 图片选择模式 - 整个区域可点击选择图片
        Button(action: {
          viewModel.showImagePicker = true
        }) {
          HStack(spacing: 12) {
            Text(viewModel.selectedImage != nil ? "更换图片" : "选择图片")
              .font(.system(size: 16, weight: .regular))
              .foregroundColor(.cBlack)

            Spacer()
          }
          .padding(.horizontal, 16)
          .frame(maxWidth: .infinity, minHeight: 56)
          .contentShape(Rectangle())  // 确保整个区域都可以点击
        }
        .buttonStyle(PlainButtonStyle())
      }

      // 右侧的图标类型切换器
      SegmentedSelector(
        options: CategoryFormVM.IconMode.allCases.map { .text($0.rawValue) },
        selectedIndex: Binding<Int>(
          get: {
            CategoryFormVM.IconMode.allCases.firstIndex(of: viewModel.selectedIconMode) ?? 0
          },
          set: { index in
            if index < CategoryFormVM.IconMode.allCases.count {
              viewModel.selectedIconMode = CategoryFormVM.IconMode.allCases[index]
            }
          }
        ),
        compact: true
      )
      .padding(.trailing, 12)
    }
    .background(.cWhite.opacity(0.5))
    .cornerRadius(24)
    .overlay(
      RoundedRectangle(cornerRadius: 24)
        .strokeBorder(
          (viewModel.selectedIconMode == .emoji
            && viewModel.emojiInput.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
            && !viewModel.name.isEmpty)
            ? .cAccentRed.opacity(0.5) : .cAccentBlue.opacity(0.08),
          lineWidth: 1
        )
    )
  }

}
#if DEBUG
  struct CategoryFormView_Previews: PreviewProvider {
    static var previews: some View {
      Group {
        // 创建主分类 - 支出类型
        CategoryFormView(
          viewModel: CategoryFormVM(
            mode: .create(isMainCategory: true, mainCategoryId: nil, selectedType: .expense),
            dataManager: createPreviewDataManager(),
            modelContext: try! ModelContainer(for: TransactionMainCategoryModel.self).mainContext
          ),
          mode: .create(isMainCategory: true, mainCategoryId: nil, selectedType: .expense)
        )
        .withDataManager(createPreviewDataManager())
        .previewDisplayName("创建主分类 - 支出")

        // 创建子分类 - 支出类型
        CategoryFormView(
          viewModel: CategoryFormVM(
            mode: .create(
              isMainCategory: false, mainCategoryId: "expense_shopping", selectedType: .expense),
            dataManager: createPreviewDataManager(),
            modelContext: try! ModelContainer(for: TransactionMainCategoryModel.self).mainContext
          ),
          mode: .create(
            isMainCategory: false, mainCategoryId: "expense_shopping", selectedType: .expense)
        )
        .withDataManager(createPreviewDataManager())
        .previewDisplayName("创建子分类 - 支出")

        // 编辑主分类
        CategoryFormView(
          viewModel: CategoryFormVM(
            mode: .edit(categoryId: "expense_shopping", isMainCategory: true),
            dataManager: createPreviewDataManager(),
            modelContext: try! ModelContainer(for: TransactionMainCategoryModel.self).mainContext
          ),
          mode: .edit(categoryId: "expense_shopping", isMainCategory: true)
        )
        .withDataManager(createPreviewDataManager())
        .previewDisplayName("编辑主分类")

        // 编辑子分类
        CategoryFormView(
          viewModel: CategoryFormVM(
            mode: .edit(categoryId: "shopping_daily", isMainCategory: false),
            dataManager: createPreviewDataManager(),
            modelContext: try! ModelContainer(for: TransactionMainCategoryModel.self).mainContext
          ),
          mode: .edit(categoryId: "shopping_daily", isMainCategory: false)
        )
        .withDataManager(createPreviewDataManager())
        .previewDisplayName("编辑子分类")

        // 创建收入主分类
        CategoryFormView(
          viewModel: CategoryFormVM(
            mode: .create(isMainCategory: true, mainCategoryId: nil, selectedType: .income),
            dataManager: createPreviewDataManager(),
            modelContext: try! ModelContainer(for: TransactionMainCategoryModel.self).mainContext
          ),
          mode: .create(isMainCategory: true, mainCategoryId: nil, selectedType: .income)
        )
        .withDataManager(createPreviewDataManager())
        .previewDisplayName("创建主分类 - 收入")
      }
    }

    /// 创建预览用的DataManagement
    static func createPreviewDataManager() -> DataManagement {
      // 创建示例主分类
      let mainCategories = [
        TransactionMainCategoryModel(
          id: "expense_shopping",
          name: "购物",
          icon: .emoji("🛒"),
          order: 0,
          type: "expense"
        ),
        TransactionMainCategoryModel(
          id: "expense_food",
          name: "餐饮",
          icon: .emoji("🍽️"),
          order: 1,
          type: "expense"
        ),
        TransactionMainCategoryModel(
          id: "income_salary",
          name: "工资收入",
          icon: .emoji("💰"),
          order: 0,
          type: "income"
        ),
      ]

      // 创建示例子分类
      let subCategories = [
        TransactionSubCategoryModel(
          id: "shopping_daily",
          name: "日常用品",
          icon: .emoji("🧴"),
          order: 0,
          mainId: "expense_shopping"
        ),
        TransactionSubCategoryModel(
          id: "shopping_clothes",
          name: "服装",
          icon: .emoji("👕"),
          order: 1,
          mainId: "expense_shopping"
        ),
        TransactionSubCategoryModel(
          id: "food_restaurant",
          name: "餐厅",
          icon: .emoji("🏪"),
          order: 0,
          mainId: "expense_food"
        ),
      ]

      return DataManagement(
        cards: [],
        mainCategories: mainCategories,
        subCategories: subCategories,
        currencies: [],
        recentTransactions: [],
        allTransactions: [],
        chatMessages: []
      )
    }
  }
#endif
