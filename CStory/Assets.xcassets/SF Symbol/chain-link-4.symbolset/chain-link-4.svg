<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg
PUBLIC "-//W3C//DTD SVG 1.1//EN"
     "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
     <!--Created with SF Symbol Generator (v1.0.0)-->

<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="3300" height="2200">
<!--glyph: "chain-link-4.medium", point size: 100.0-->
<style>.SFSymbolsPreviewWireframe {fill:none;opacity:1.0;stroke:black;stroke-width:0.5}
</style>
<g id="Notes">
<rect height="2200" id="artboard" style="fill:white;opacity:1" width="3300" x="0" y="0"/>
<line style="fill:none;stroke:black;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="292" y2="292"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 263 322)">Weight/Scale Variations</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 559.711 322)">Ultralight</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 856.422 322)">Thin</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1153.13 322)">Light</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1449.84 322)">Regular</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1746.56 322)">Medium</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2043.27 322)">Semibold</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2339.98 322)">Bold</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2636.69 322)">Heavy</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2933.4 322)">Black</text>
<line style="fill:none;stroke:black;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1903" y2="1903"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 263 1953)">Design Variations</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1971)">Symbols are supported in up to nine weights and three scales.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1989)">For optimal layout with text and other symbols, vertically align</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 2007)">symbols with the adjacent text.</text>
<line style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="776" x2="776" y1="1919" y2="1933"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 776 1953)">Margins</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 1971)">Leading and trailing margins on the left and right side of each symbol</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 1989)">can be adjusted by modifying the x-location of the margin guidelines.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 2007)">Modifications are automatically applied proportionally to all</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 2025)">scales and weights.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 1289 1953)">Exporting</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 1289 1971)">Symbols should be outlined when exporting to ensure the</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 1289 1989)">design is preserved when submitting to Xcode.</text>
<text id="template-version" style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1933)">Template v.5.0</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1951)">Requires Xcode 15 or greater</text>
<text id="descriptive-name" style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1969)">Generated from chain-link-4</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1987)">Typeset at 100.0 points</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 726)">Small</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1156)">Medium</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1586)">Large</text>
</g>
<g id="Guides">
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 696)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-S" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="696" y2="696"/>
<line id="Capline-S" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="625.541" y2="625.541"/>
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 1126)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-M" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1126" y2="1126"/>
<line id="Capline-M" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1055.54" y2="1055.54"/>
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 1556)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-L" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1556" y2="1556"/>
<line id="Capline-L" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1485.54" y2="1485.54"/>
<line id="left-margin-Ultralight-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="513.711" x2="513.711" y1="600.785" y2="720.121"/>
<line id="right-margin-Ultralight-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="605.711" x2="605.711" y1="600.785" y2="720.121"/>
<line id="left-margin-Regular-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="1403.84" x2="1403.84" y1="600.785" y2="720.121"/>
<line id="right-margin-Regular-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="1495.84" x2="1495.84" y1="600.785" y2="720.121"/>
<line id="left-margin-Black-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="2887.4" x2="2887.4" y1="600.785" y2="720.121"/>
<line id="right-margin-Black-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="2979.4" x2="2979.4" y1="600.785" y2="720.121"/>
</g>
<g id="Symbols">
<g id="Ultralight-S" transform="matrix(1 0 0 1 513.711 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="chain-link-4">
<g id="Union">
<path d="M25.459 35.6442C32.5699 28.5348 44.1001 28.5339 51.2105 35.6442L52.5244 36.9582L53.0672 37.5235C55.7093 40.3999 57.2611 43.927 57.7166 47.5598C57.9138 49.1346 56.7943 50.5697 55.2197 50.7679C53.6448 50.9647 52.2092 49.8497 52.0116 48.2748C51.7206 45.9549 50.7771 43.705 49.1815 41.8135L48.459 41.0236L47.145 39.7097C42.2802 34.8448 34.3898 34.8457 29.5244 39.7097L16.7105 52.5236C11.8452 57.3889 11.8452 65.2789 16.7105 70.1442L18.0244 71.4582C22.8897 76.3234 30.7798 76.3235 35.645 71.4582L36.3001 70.7993C37.4232 69.6782 39.2472 69.6768 40.3693 70.7993C41.4913 71.9219 41.4872 73.7459 40.3656 74.8685L39.7105 75.5236C32.5997 82.6344 21.0698 82.6344 13.959 75.5236L12.645 74.2097C5.53427 67.0989 5.53421 55.569 12.645 48.4582L25.459 35.6442Z" fill="black"/>
<path d="M52.9736 19.6633C60.1215 13.2076 71.156 13.4231 78.0438 20.3109L79.3578 21.6249C86.4681 28.7352 86.4672 40.2654 79.3578 47.3763L66.5438 60.1903C59.433 67.3011 47.9031 67.301 40.7923 60.1903L39.4784 58.8763C36.5054 55.9034 34.7716 52.1492 34.2861 48.2748C34.0894 46.6997 35.2079 45.2602 36.783 45.0628C38.3568 44.8676 39.7935 45.986 39.9912 47.5598C40.3234 50.2109 41.506 52.7731 43.5438 54.8109L44.8578 56.1249C49.723 60.9901 57.6131 60.9901 62.4784 56.1249L75.2923 43.3109C80.1563 38.4455 80.1571 30.5551 75.2923 25.6903L73.9784 24.3763C69.1135 19.5115 61.2232 19.5124 56.3578 24.3763L55.7026 25.0314C54.5807 26.1538 52.7603 26.156 51.6372 25.0352C50.5142 23.9126 50.5109 22.089 51.6335 20.966L52.2923 20.3109L52.9736 19.6633Z" fill="black"/>
</g>
</g>
</svg>

</g>
<g id="Regular-S" transform="matrix(1 0 0 1 1403.84 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="chain-link-4">
<g id="Union">
<path d="M25.459 35.6442C32.5699 28.5348 44.1001 28.5339 51.2105 35.6442L52.5244 36.9582L53.0672 37.5235C55.7093 40.3999 57.2611 43.927 57.7166 47.5598C57.9138 49.1346 56.7943 50.5697 55.2197 50.7679C53.6448 50.9647 52.2092 49.8497 52.0116 48.2748C51.7206 45.9549 50.7771 43.705 49.1815 41.8135L48.459 41.0236L47.145 39.7097C42.2802 34.8448 34.3898 34.8457 29.5244 39.7097L16.7105 52.5236C11.8452 57.3889 11.8452 65.2789 16.7105 70.1442L18.0244 71.4582C22.8897 76.3234 30.7798 76.3235 35.645 71.4582L36.3001 70.7993C37.4232 69.6782 39.2472 69.6768 40.3693 70.7993C41.4913 71.9219 41.4872 73.7459 40.3656 74.8685L39.7105 75.5236C32.5997 82.6344 21.0698 82.6344 13.959 75.5236L12.645 74.2097C5.53427 67.0989 5.53421 55.569 12.645 48.4582L25.459 35.6442Z" fill="black"/>
<path d="M52.9736 19.6633C60.1215 13.2076 71.156 13.4231 78.0438 20.3109L79.3578 21.6249C86.4681 28.7352 86.4672 40.2654 79.3578 47.3763L66.5438 60.1903C59.433 67.3011 47.9031 67.301 40.7923 60.1903L39.4784 58.8763C36.5054 55.9034 34.7716 52.1492 34.2861 48.2748C34.0894 46.6997 35.2079 45.2602 36.783 45.0628C38.3568 44.8676 39.7935 45.986 39.9912 47.5598C40.3234 50.2109 41.506 52.7731 43.5438 54.8109L44.8578 56.1249C49.723 60.9901 57.6131 60.9901 62.4784 56.1249L75.2923 43.3109C80.1563 38.4455 80.1571 30.5551 75.2923 25.6903L73.9784 24.3763C69.1135 19.5115 61.2232 19.5124 56.3578 24.3763L55.7026 25.0314C54.5807 26.1538 52.7603 26.156 51.6372 25.0352C50.5142 23.9126 50.5109 22.089 51.6335 20.966L52.2923 20.3109L52.9736 19.6633Z" fill="black"/>
</g>
</g>
</svg>

</g>
<g id="Black-S" transform="matrix(1 0 0 1 2887.4 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="chain-link-4">
<g id="Union">
<path d="M25.459 35.6442C32.5699 28.5348 44.1001 28.5339 51.2105 35.6442L52.5244 36.9582L53.0672 37.5235C55.7093 40.3999 57.2611 43.927 57.7166 47.5598C57.9138 49.1346 56.7943 50.5697 55.2197 50.7679C53.6448 50.9647 52.2092 49.8497 52.0116 48.2748C51.7206 45.9549 50.7771 43.705 49.1815 41.8135L48.459 41.0236L47.145 39.7097C42.2802 34.8448 34.3898 34.8457 29.5244 39.7097L16.7105 52.5236C11.8452 57.3889 11.8452 65.2789 16.7105 70.1442L18.0244 71.4582C22.8897 76.3234 30.7798 76.3235 35.645 71.4582L36.3001 70.7993C37.4232 69.6782 39.2472 69.6768 40.3693 70.7993C41.4913 71.9219 41.4872 73.7459 40.3656 74.8685L39.7105 75.5236C32.5997 82.6344 21.0698 82.6344 13.959 75.5236L12.645 74.2097C5.53427 67.0989 5.53421 55.569 12.645 48.4582L25.459 35.6442Z" fill="black"/>
<path d="M52.9736 19.6633C60.1215 13.2076 71.156 13.4231 78.0438 20.3109L79.3578 21.6249C86.4681 28.7352 86.4672 40.2654 79.3578 47.3763L66.5438 60.1903C59.433 67.3011 47.9031 67.301 40.7923 60.1903L39.4784 58.8763C36.5054 55.9034 34.7716 52.1492 34.2861 48.2748C34.0894 46.6997 35.2079 45.2602 36.783 45.0628C38.3568 44.8676 39.7935 45.986 39.9912 47.5598C40.3234 50.2109 41.506 52.7731 43.5438 54.8109L44.8578 56.1249C49.723 60.9901 57.6131 60.9901 62.4784 56.1249L75.2923 43.3109C80.1563 38.4455 80.1571 30.5551 75.2923 25.6903L73.9784 24.3763C69.1135 19.5115 61.2232 19.5124 56.3578 24.3763L55.7026 25.0314C54.5807 26.1538 52.7603 26.156 51.6372 25.0352C50.5142 23.9126 50.5109 22.089 51.6335 20.966L52.2923 20.3109L52.9736 19.6633Z" fill="black"/>
</g>
</g>
</svg>

</g>
</g>
</svg>