<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg
PUBLIC "-//W3C//DTD SVG 1.1//EN"
     "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
     <!--Created with SF Symbol Generator (v1.0.0)-->

<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="3300" height="2200">
<!--glyph: "square-grid-circle.medium", point size: 100.0-->
<style>.SFSymbolsPreviewWireframe {fill:none;opacity:1.0;stroke:black;stroke-width:0.5}
</style>
<g id="Notes">
<rect height="2200" id="artboard" style="fill:white;opacity:1" width="3300" x="0" y="0"/>
<line style="fill:none;stroke:black;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="292" y2="292"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 263 322)">Weight/Scale Variations</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 559.711 322)">Ultralight</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 856.422 322)">Thin</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1153.13 322)">Light</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1449.84 322)">Regular</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1746.56 322)">Medium</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2043.27 322)">Semibold</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2339.98 322)">Bold</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2636.69 322)">Heavy</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2933.4 322)">Black</text>
<line style="fill:none;stroke:black;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1903" y2="1903"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 263 1953)">Design Variations</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1971)">Symbols are supported in up to nine weights and three scales.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1989)">For optimal layout with text and other symbols, vertically align</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 2007)">symbols with the adjacent text.</text>
<line style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="776" x2="776" y1="1919" y2="1933"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 776 1953)">Margins</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 1971)">Leading and trailing margins on the left and right side of each symbol</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 1989)">can be adjusted by modifying the x-location of the margin guidelines.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 2007)">Modifications are automatically applied proportionally to all</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 2025)">scales and weights.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 1289 1953)">Exporting</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 1289 1971)">Symbols should be outlined when exporting to ensure the</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 1289 1989)">design is preserved when submitting to Xcode.</text>
<text id="template-version" style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1933)">Template v.5.0</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1951)">Requires Xcode 15 or greater</text>
<text id="descriptive-name" style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1969)">Generated from square-grid-circle</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1987)">Typeset at 100.0 points</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 726)">Small</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1156)">Medium</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1586)">Large</text>
</g>
<g id="Guides">
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 696)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-S" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="696" y2="696"/>
<line id="Capline-S" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="625.541" y2="625.541"/>
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 1126)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-M" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1126" y2="1126"/>
<line id="Capline-M" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1055.54" y2="1055.54"/>
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 1556)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-L" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1556" y2="1556"/>
<line id="Capline-L" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1485.54" y2="1485.54"/>
<line id="left-margin-Ultralight-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="513.711" x2="513.711" y1="600.785" y2="720.121"/>
<line id="right-margin-Ultralight-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="605.711" x2="605.711" y1="600.785" y2="720.121"/>
<line id="left-margin-Regular-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="1403.84" x2="1403.84" y1="600.785" y2="720.121"/>
<line id="right-margin-Regular-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="1495.84" x2="1495.84" y1="600.785" y2="720.121"/>
<line id="left-margin-Black-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="2887.4" x2="2887.4" y1="600.785" y2="720.121"/>
<line id="right-margin-Black-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="2979.4" x2="2979.4" y1="600.785" y2="720.121"/>
</g>
<g id="Symbols">
<g id="Ultralight-S" transform="matrix(1 0 0 1 513.711 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="square-grid-circle">
<g id="Union">
<path fill-rule="evenodd" clip-rule="evenodd" d="M31.625 49.8333C37.447 49.8333 42.1667 54.5529 42.1667 60.375V69.9583C42.1667 75.7805 37.447 80.5 31.625 80.5H22.0417C16.2197 80.5 11.5 75.7805 11.5 69.9583V60.375C11.5 54.5529 16.2197 49.8333 22.0417 49.8333H31.625ZM22.0417 55.5833C19.3953 55.5833 17.25 57.7285 17.25 60.375V69.9583C17.25 72.6048 19.3953 74.75 22.0417 74.75H31.625C34.2714 74.75 36.4167 72.6048 36.4167 69.9583V60.375C36.4167 57.7285 34.2714 55.5833 31.625 55.5833H22.0417Z" fill="black"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M65.1667 49.8333C73.6349 49.8333 80.5 56.6984 80.5 65.1667C80.5 73.6349 73.6349 80.5 65.1667 80.5C56.6984 80.5 49.8333 73.6349 49.8333 65.1667C49.8333 56.6984 56.6984 49.8333 65.1667 49.8333ZM65.1667 55.5833C59.874 55.5833 55.5833 59.874 55.5833 65.1667C55.5833 70.4593 59.874 74.75 65.1667 74.75C70.4593 74.75 74.75 70.4593 74.75 65.1667C74.75 59.874 70.4593 55.5833 65.1667 55.5833Z" fill="black"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M31.625 11.5C37.447 11.5 42.1667 16.2197 42.1667 22.0417V31.625C42.1667 37.447 37.447 42.1667 31.625 42.1667H22.0417C16.2197 42.1667 11.5 37.447 11.5 31.625V22.0417C11.5 16.2197 16.2197 11.5 22.0417 11.5H31.625ZM22.0417 17.25C19.3953 17.25 17.25 19.3953 17.25 22.0417V31.625C17.25 34.2714 19.3953 36.4167 22.0417 36.4167H31.625C34.2714 36.4167 36.4167 34.2714 36.4167 31.625V22.0417C36.4167 19.3953 34.2714 17.25 31.625 17.25H22.0417Z" fill="black"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M69.9583 11.5C75.7805 11.5 80.5 16.2197 80.5 22.0417V31.625C80.5 37.447 75.7805 42.1667 69.9583 42.1667H60.375C54.5529 42.1667 49.8333 37.447 49.8333 31.625V22.0417C49.8333 16.2197 54.5529 11.5 60.375 11.5H69.9583ZM60.375 17.25C57.7285 17.25 55.5833 19.3953 55.5833 22.0417V31.625C55.5833 34.2714 57.7285 36.4167 60.375 36.4167H69.9583C72.6048 36.4167 74.75 34.2714 74.75 31.625V22.0417C74.75 19.3953 72.6048 17.25 69.9583 17.25H60.375Z" fill="black"/>
</g>
</g>
</svg>

</g>
<g id="Regular-S" transform="matrix(1 0 0 1 1403.84 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="square-grid-circle">
<g id="Union">
<path fill-rule="evenodd" clip-rule="evenodd" d="M31.625 49.8333C37.447 49.8333 42.1667 54.5529 42.1667 60.375V69.9583C42.1667 75.7805 37.447 80.5 31.625 80.5H22.0417C16.2197 80.5 11.5 75.7805 11.5 69.9583V60.375C11.5 54.5529 16.2197 49.8333 22.0417 49.8333H31.625ZM22.0417 55.5833C19.3953 55.5833 17.25 57.7285 17.25 60.375V69.9583C17.25 72.6048 19.3953 74.75 22.0417 74.75H31.625C34.2714 74.75 36.4167 72.6048 36.4167 69.9583V60.375C36.4167 57.7285 34.2714 55.5833 31.625 55.5833H22.0417Z" fill="black"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M65.1667 49.8333C73.6349 49.8333 80.5 56.6984 80.5 65.1667C80.5 73.6349 73.6349 80.5 65.1667 80.5C56.6984 80.5 49.8333 73.6349 49.8333 65.1667C49.8333 56.6984 56.6984 49.8333 65.1667 49.8333ZM65.1667 55.5833C59.874 55.5833 55.5833 59.874 55.5833 65.1667C55.5833 70.4593 59.874 74.75 65.1667 74.75C70.4593 74.75 74.75 70.4593 74.75 65.1667C74.75 59.874 70.4593 55.5833 65.1667 55.5833Z" fill="black"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M31.625 11.5C37.447 11.5 42.1667 16.2197 42.1667 22.0417V31.625C42.1667 37.447 37.447 42.1667 31.625 42.1667H22.0417C16.2197 42.1667 11.5 37.447 11.5 31.625V22.0417C11.5 16.2197 16.2197 11.5 22.0417 11.5H31.625ZM22.0417 17.25C19.3953 17.25 17.25 19.3953 17.25 22.0417V31.625C17.25 34.2714 19.3953 36.4167 22.0417 36.4167H31.625C34.2714 36.4167 36.4167 34.2714 36.4167 31.625V22.0417C36.4167 19.3953 34.2714 17.25 31.625 17.25H22.0417Z" fill="black"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M69.9583 11.5C75.7805 11.5 80.5 16.2197 80.5 22.0417V31.625C80.5 37.447 75.7805 42.1667 69.9583 42.1667H60.375C54.5529 42.1667 49.8333 37.447 49.8333 31.625V22.0417C49.8333 16.2197 54.5529 11.5 60.375 11.5H69.9583ZM60.375 17.25C57.7285 17.25 55.5833 19.3953 55.5833 22.0417V31.625C55.5833 34.2714 57.7285 36.4167 60.375 36.4167H69.9583C72.6048 36.4167 74.75 34.2714 74.75 31.625V22.0417C74.75 19.3953 72.6048 17.25 69.9583 17.25H60.375Z" fill="black"/>
</g>
</g>
</svg>

</g>
<g id="Black-S" transform="matrix(1 0 0 1 2887.4 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="square-grid-circle">
<g id="Union">
<path fill-rule="evenodd" clip-rule="evenodd" d="M31.625 49.8333C37.447 49.8333 42.1667 54.5529 42.1667 60.375V69.9583C42.1667 75.7805 37.447 80.5 31.625 80.5H22.0417C16.2197 80.5 11.5 75.7805 11.5 69.9583V60.375C11.5 54.5529 16.2197 49.8333 22.0417 49.8333H31.625ZM22.0417 55.5833C19.3953 55.5833 17.25 57.7285 17.25 60.375V69.9583C17.25 72.6048 19.3953 74.75 22.0417 74.75H31.625C34.2714 74.75 36.4167 72.6048 36.4167 69.9583V60.375C36.4167 57.7285 34.2714 55.5833 31.625 55.5833H22.0417Z" fill="black"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M65.1667 49.8333C73.6349 49.8333 80.5 56.6984 80.5 65.1667C80.5 73.6349 73.6349 80.5 65.1667 80.5C56.6984 80.5 49.8333 73.6349 49.8333 65.1667C49.8333 56.6984 56.6984 49.8333 65.1667 49.8333ZM65.1667 55.5833C59.874 55.5833 55.5833 59.874 55.5833 65.1667C55.5833 70.4593 59.874 74.75 65.1667 74.75C70.4593 74.75 74.75 70.4593 74.75 65.1667C74.75 59.874 70.4593 55.5833 65.1667 55.5833Z" fill="black"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M31.625 11.5C37.447 11.5 42.1667 16.2197 42.1667 22.0417V31.625C42.1667 37.447 37.447 42.1667 31.625 42.1667H22.0417C16.2197 42.1667 11.5 37.447 11.5 31.625V22.0417C11.5 16.2197 16.2197 11.5 22.0417 11.5H31.625ZM22.0417 17.25C19.3953 17.25 17.25 19.3953 17.25 22.0417V31.625C17.25 34.2714 19.3953 36.4167 22.0417 36.4167H31.625C34.2714 36.4167 36.4167 34.2714 36.4167 31.625V22.0417C36.4167 19.3953 34.2714 17.25 31.625 17.25H22.0417Z" fill="black"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M69.9583 11.5C75.7805 11.5 80.5 16.2197 80.5 22.0417V31.625C80.5 37.447 75.7805 42.1667 69.9583 42.1667H60.375C54.5529 42.1667 49.8333 37.447 49.8333 31.625V22.0417C49.8333 16.2197 54.5529 11.5 60.375 11.5H69.9583ZM60.375 17.25C57.7285 17.25 55.5833 19.3953 55.5833 22.0417V31.625C55.5833 34.2714 57.7285 36.4167 60.375 36.4167H69.9583C72.6048 36.4167 74.75 34.2714 74.75 31.625V22.0417C74.75 19.3953 72.6048 17.25 69.9583 17.25H60.375Z" fill="black"/>
</g>
</g>
</svg>

</g>
</g>
</svg>