<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg
PUBLIC "-//W3C//DTD SVG 1.1//EN"
     "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
     <!--Created with SF Symbol Generator (v1.0.0)-->

<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="3300" height="2200">
<!--glyph: "arrow-down-right.medium", point size: 100.0-->
<style>.SFSymbolsPreviewWireframe {fill:none;opacity:1.0;stroke:black;stroke-width:0.5}
</style>
<g id="Notes">
<rect height="2200" id="artboard" style="fill:white;opacity:1" width="3300" x="0" y="0"/>
<line style="fill:none;stroke:black;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="292" y2="292"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 263 322)">Weight/Scale Variations</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 559.711 322)">Ultralight</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 856.422 322)">Thin</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1153.13 322)">Light</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1449.84 322)">Regular</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1746.56 322)">Medium</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2043.27 322)">Semibold</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2339.98 322)">Bold</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2636.69 322)">Heavy</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2933.4 322)">Black</text>
<line style="fill:none;stroke:black;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1903" y2="1903"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 263 1953)">Design Variations</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1971)">Symbols are supported in up to nine weights and three scales.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1989)">For optimal layout with text and other symbols, vertically align</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 2007)">symbols with the adjacent text.</text>
<line style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="776" x2="776" y1="1919" y2="1933"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 776 1953)">Margins</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 1971)">Leading and trailing margins on the left and right side of each symbol</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 1989)">can be adjusted by modifying the x-location of the margin guidelines.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 2007)">Modifications are automatically applied proportionally to all</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 2025)">scales and weights.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 1289 1953)">Exporting</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 1289 1971)">Symbols should be outlined when exporting to ensure the</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 1289 1989)">design is preserved when submitting to Xcode.</text>
<text id="template-version" style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1933)">Template v.5.0</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1951)">Requires Xcode 15 or greater</text>
<text id="descriptive-name" style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1969)">Generated from arrow-down-right</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1987)">Typeset at 100.0 points</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 726)">Small</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1156)">Medium</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1586)">Large</text>
</g>
<g id="Guides">
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 696)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-S" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="696" y2="696"/>
<line id="Capline-S" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="625.541" y2="625.541"/>
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 1126)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-M" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1126" y2="1126"/>
<line id="Capline-M" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1055.54" y2="1055.54"/>
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 1556)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-L" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1556" y2="1556"/>
<line id="Capline-L" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1485.54" y2="1485.54"/>
<line id="left-margin-Ultralight-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="513.711" x2="513.711" y1="600.785" y2="720.121"/>
<line id="right-margin-Ultralight-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="605.711" x2="605.711" y1="600.785" y2="720.121"/>
<line id="left-margin-Regular-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="1403.84" x2="1403.84" y1="600.785" y2="720.121"/>
<line id="right-margin-Regular-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="1495.84" x2="1495.84" y1="600.785" y2="720.121"/>
<line id="left-margin-Black-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="2887.4" x2="2887.4" y1="600.785" y2="720.121"/>
<line id="right-margin-Black-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="2979.4" x2="2979.4" y1="600.785" y2="720.121"/>
</g>
<g id="Symbols">
<g id="Ultralight-S" transform="matrix(1 0 0 1 513.711 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="arrow-down-right">
<path id="Subtract" fill-rule="evenodd" clip-rule="evenodd" d="M66.125 11.5C74.0643 11.5 80.5 17.936 80.5 25.875V66.125C80.5 74.0643 74.0643 80.5 66.125 80.5H25.875C17.936 80.5 11.5 74.0643 11.5 66.125V25.875C11.5 17.9359 17.9359 11.5 25.875 11.5H66.125ZM33.1785 29.1131C32.0558 27.9905 30.2358 27.9905 29.1131 29.1131C27.9904 30.2358 27.9905 32.0558 29.1131 33.1785L54.8721 58.9375H38.8125C37.2248 58.9375 35.9376 60.2248 35.9375 61.8125C35.9376 63.4002 37.2248 64.6875 38.8125 64.6875H61.8125L62.1045 64.6725C62.1754 64.6654 62.2448 64.6511 62.3141 64.6388C62.3366 64.6349 62.3592 64.6321 62.3815 64.6276C62.4328 64.6173 62.481 64.5957 62.5312 64.5827C62.6587 64.5497 62.7865 64.5174 62.9093 64.4666C63.1024 64.3868 63.283 64.2852 63.4521 64.1672C63.5891 64.0716 63.723 63.9674 63.8452 63.8452C63.9674 63.723 64.0716 63.589 64.1672 63.4521C64.2851 63.283 64.3868 63.1023 64.4666 62.9093C64.5463 62.7165 64.603 62.517 64.6388 62.3141C64.6676 62.151 64.6875 61.9838 64.6875 61.8125V38.8125C64.6874 37.2249 63.4001 35.9376 61.8125 35.9375C60.2248 35.9375 58.9376 37.2248 58.9375 38.8125V54.8721L33.1785 29.1131Z" fill="black"/>
</g>
</svg>

</g>
<g id="Regular-S" transform="matrix(1 0 0 1 1403.84 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="arrow-down-right">
<path id="Subtract" fill-rule="evenodd" clip-rule="evenodd" d="M66.125 11.5C74.0643 11.5 80.5 17.936 80.5 25.875V66.125C80.5 74.0643 74.0643 80.5 66.125 80.5H25.875C17.936 80.5 11.5 74.0643 11.5 66.125V25.875C11.5 17.9359 17.9359 11.5 25.875 11.5H66.125ZM33.1785 29.1131C32.0558 27.9905 30.2358 27.9905 29.1131 29.1131C27.9904 30.2358 27.9905 32.0558 29.1131 33.1785L54.8721 58.9375H38.8125C37.2248 58.9375 35.9376 60.2248 35.9375 61.8125C35.9376 63.4002 37.2248 64.6875 38.8125 64.6875H61.8125L62.1045 64.6725C62.1754 64.6654 62.2448 64.6511 62.3141 64.6388C62.3366 64.6349 62.3592 64.6321 62.3815 64.6276C62.4328 64.6173 62.481 64.5957 62.5312 64.5827C62.6587 64.5497 62.7865 64.5174 62.9093 64.4666C63.1024 64.3868 63.283 64.2852 63.4521 64.1672C63.5891 64.0716 63.723 63.9674 63.8452 63.8452C63.9674 63.723 64.0716 63.589 64.1672 63.4521C64.2851 63.283 64.3868 63.1023 64.4666 62.9093C64.5463 62.7165 64.603 62.517 64.6388 62.3141C64.6676 62.151 64.6875 61.9838 64.6875 61.8125V38.8125C64.6874 37.2249 63.4001 35.9376 61.8125 35.9375C60.2248 35.9375 58.9376 37.2248 58.9375 38.8125V54.8721L33.1785 29.1131Z" fill="black"/>
</g>
</svg>

</g>
<g id="Black-S" transform="matrix(1 0 0 1 2887.4 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="arrow-down-right">
<path id="Subtract" fill-rule="evenodd" clip-rule="evenodd" d="M66.125 11.5C74.0643 11.5 80.5 17.936 80.5 25.875V66.125C80.5 74.0643 74.0643 80.5 66.125 80.5H25.875C17.936 80.5 11.5 74.0643 11.5 66.125V25.875C11.5 17.9359 17.9359 11.5 25.875 11.5H66.125ZM33.1785 29.1131C32.0558 27.9905 30.2358 27.9905 29.1131 29.1131C27.9904 30.2358 27.9905 32.0558 29.1131 33.1785L54.8721 58.9375H38.8125C37.2248 58.9375 35.9376 60.2248 35.9375 61.8125C35.9376 63.4002 37.2248 64.6875 38.8125 64.6875H61.8125L62.1045 64.6725C62.1754 64.6654 62.2448 64.6511 62.3141 64.6388C62.3366 64.6349 62.3592 64.6321 62.3815 64.6276C62.4328 64.6173 62.481 64.5957 62.5312 64.5827C62.6587 64.5497 62.7865 64.5174 62.9093 64.4666C63.1024 64.3868 63.283 64.2852 63.4521 64.1672C63.5891 64.0716 63.723 63.9674 63.8452 63.8452C63.9674 63.723 64.0716 63.589 64.1672 63.4521C64.2851 63.283 64.3868 63.1023 64.4666 62.9093C64.5463 62.7165 64.603 62.517 64.6388 62.3141C64.6676 62.151 64.6875 61.9838 64.6875 61.8125V38.8125C64.6874 37.2249 63.4001 35.9376 61.8125 35.9375C60.2248 35.9375 58.9376 37.2248 58.9375 38.8125V54.8721L33.1785 29.1131Z" fill="black"/>
</g>
</svg>

</g>
</g>
</svg>