//
//  ThemeManager.swift
//  CStory
//
//  Created by AI Assistant on 2025/8/13.
//

import SwiftUI

/// 主题模式枚举
///
/// 定义应用支持的主题模式，包括浅色、深色和系统自动模式。
/// 每个模式都有对应的显示名称、图标和描述信息。
enum AppThemeMode: String, CaseIterable {
  case light = "light"
  case dark = "dark"
  case system = "system"

  /// 图标名称
  var iconName: String {
    switch self {
    case .light:
      return "sun"
    case .dark:
      return "moon"
    case .system:
      return "settings-gear-2"
    }
  }

  /// 描述信息
  var description: String {
    switch self {
    case .light:
      return "始终使用浅色主题"
    case .dark:
      return "始终使用深色主题"
    case .system:
      return "跟随系统设置自动切换"
    }
  }

  /// 转换为SwiftUI的ColorScheme
  var colorScheme: ColorScheme? {
    switch self {
    case .light:
      return .light
    case .dark:
      return .dark
    case .system:
      return nil  // 使用系统默认
    }
  }
}

/// 主题管理工具类
///
/// 提供简单的主题管理功能，使用UserDefaults进行持久化存储。
/// 避免复杂的响应式架构，直接使用SwiftUI的@AppStorage。
class ThemeHelper {

  /// UserDefaults存储键
  static let themeKey = "app_theme_mode"

  /// 获取当前保存的主题模式
  static func getCurrentTheme() -> AppThemeMode {
    let savedTheme = UserDefaults.standard.string(forKey: themeKey) ?? AppThemeMode.system.rawValue
    return AppThemeMode(rawValue: savedTheme) ?? .system
  }

  /// 保存主题模式
  static func saveTheme(_ theme: AppThemeMode) {
    UserDefaults.standard.set(theme.rawValue, forKey: themeKey)
  }
}
