//
//  SegmentedSelector.swift
//  CStory
//
//  Created by AI Assistant on 2025/8/1.
//

import SwiftUI

/// 分段选择器选项类型
enum SegmentedOption {
  /// 文字选项
  case text(String)
  /// 图标选项
  case icon(String)
}

/// 通用分段选择器组件
///
/// 提供两个或多个选项之间的切换功能，支持：
/// - 动画切换效果
/// - 触觉反馈
/// - 按压动画
/// - 文字和图标选项
struct SegmentedSelector: View {

  // MARK: - Properties

  let options: [SegmentedOption]
  @Binding var selectedIndex: Int
  let onSelectionChanged: ((Int) -> Void)?
  let compact: Bool  // 是否为紧凑模式（用于StatusCardRow）

  // 内部状态管理（用于非绑定模式）
  @State private var internalSelectedIndex: Int
  private let useInternalState: Bool

  /// 动画命名空间
  @Namespace private var animation

  /// 当前按压的选项索引
  @State private var pressingIndex: Int?

  // MARK: - Initialization

  init(
    options: [SegmentedOption],
    selectedIndex: Binding<Int>,
    compact: Bool = false,
    onSelectionChanged: ((Int) -> Void)? = nil
  ) {
    self.options = options
    self._selectedIndex = selectedIndex
    self.compact = compact
    self.onSelectionChanged = onSelectionChanged
    self.useInternalState = false
    self._internalSelectedIndex = State(initialValue: selectedIndex.wrappedValue)
  }

  /// 非绑定模式初始化（用于StatusCardRow）
  init(
    options: [SegmentedOption],
    initialSelectedIndex: Int,
    compact: Bool = false,
    onSelectionChanged: ((Int) -> Void)? = nil
  ) {
    self.options = options
    self._selectedIndex = .constant(initialSelectedIndex)
    self.compact = compact
    self.onSelectionChanged = onSelectionChanged
    self.useInternalState = true
    self._internalSelectedIndex = State(initialValue: initialSelectedIndex)
  }

  /// 便捷初始化方法 - 文字选项（向后兼容）
  init(
    options: [String],
    selectedIndex: Binding<Int>,
    compact: Bool = false,
    onSelectionChanged: ((Int) -> Void)? = nil
  ) {
    self.options = options.map { .text($0) }
    self._selectedIndex = selectedIndex
    self.compact = compact
    self.onSelectionChanged = onSelectionChanged
    self.useInternalState = false
    self._internalSelectedIndex = State(initialValue: selectedIndex.wrappedValue)
  }

  // MARK: - Body

  var body: some View {
    HStack(spacing: compact ? 4 : 8) {
      ForEach(Array(options.enumerated()), id: \.offset) { index, option in
        optionButton(for: option, at: index)
      }
    }
    .padding(compact ? 2 : 2)
    .background(compact ? .cAccentBlue.opacity(0.1) : .cWhite.opacity(0.5))
    .cornerRadius(compact ? 16 : 24)
    .overlay(
      RoundedRectangle(cornerRadius: compact ? 16 : 24)
        .strokeBorder(.cAccentBlue.opacity(0.08), lineWidth: compact ? 0 : 1)
    )
    .padding(.horizontal, compact ? 0 : 16)
    .padding(.vertical, compact ? 0 : 12)
  }

  // MARK: - Private Methods

  /// 创建选项按钮
  private func optionButton(for option: SegmentedOption, at index: Int) -> some View {
    Button {
      selectOption(at: index)
    } label: {
      optionContent(for: option)
        .font(.system(size: compact ? 14 : 16, weight: .medium))
        .foregroundColor(
          currentSelectedIndex == index ? .cWhite : .cBlack.opacity(0.6)
        )
        .frame(width: compact ? optionWidth : nil)
        .frame(maxWidth: compact ? nil : .infinity)
        .frame(height: compact ? 28 : 34)
        .background(
          ZStack {
            if currentSelectedIndex == index {
              Color.cAccentBlue
                .cornerRadius(compact ? 14 : 16)
                .matchedGeometryEffect(id: "background", in: animation)
            }
          }
        )
        .scaleEffect(pressingIndex == index ? 0.95 : 1.0)
        .animation(.easeInOut(duration: 0.1), value: pressingIndex)
    }
    .onLongPressGesture(
      minimumDuration: 0, maximumDistance: .infinity,
      pressing: { pressing in
        if pressing {
          pressingIndex = index
        } else {
          pressingIndex = nil
        }
      }, perform: {})
  }

  /// 当前选中的索引
  private var currentSelectedIndex: Int {
    useInternalState ? internalSelectedIndex : selectedIndex
  }

  /// 选项内容视图
  @ViewBuilder
  private func optionContent(for option: SegmentedOption) -> some View {
    switch option {
    case .text(let text):
      Text(text)
    case .icon(let iconName):
      Image(iconName)
        .font(.system(size: compact ? 16 : 18, weight: .medium))
    }
  }

  /// 紧凑模式下的选项宽度
  private var optionWidth: CGFloat {
    guard compact else { return 0 }
    switch options.count {
    case 2: return 45
    case 3: return 35
    default: return 40
    }
  }

  /// 选择选项
  private func selectOption(at index: Int) {
    guard currentSelectedIndex != index else { return }

    // 触觉反馈
    HapticFeedbackManager.shared.trigger(.selection)

    // 动画切换
    withAnimation(.easeInOut(duration: 0.2)) {
      if useInternalState {
        internalSelectedIndex = index
      } else {
        selectedIndex = index
      }
    }

    // 回调通知
    onSelectionChanged?(index)
  }
}

// MARK: - Preview

#Preview {
  VStack(spacing: 20) {
    // 简单的分段选择器
    SegmentedSelector(
      options: ["选项1", "选项2", "选项3"],
      selectedIndex: .constant(0),
      onSelectionChanged: { index in
        print("选择了选项: \(index)")
      }
    )

    // 交易类型选择器
    SegmentedSelector(
      options: ["支出", "收入"],
      selectedIndex: .constant(1),
      onSelectionChanged: { index in
        let type: TransactionType = index == 0 ? .expense : .income
        print("交易类型切换到: \(type)")
      }
    )
  }
  .padding()
  .background(.cLightBlue)
}
