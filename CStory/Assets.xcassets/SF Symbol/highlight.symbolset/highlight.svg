<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg
PUBLIC "-//W3C//DTD SVG 1.1//EN"
     "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
     <!--Created with SF Symbol Generator (v1.0.0)-->

<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="3300" height="2200">
<!--glyph: "highlight.medium", point size: 100.0-->
<style>.SFSymbolsPreviewWireframe {fill:none;opacity:1.0;stroke:black;stroke-width:0.5}
</style>
<g id="Notes">
<rect height="2200" id="artboard" style="fill:white;opacity:1" width="3300" x="0" y="0"/>
<line style="fill:none;stroke:black;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="292" y2="292"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 263 322)">Weight/Scale Variations</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 559.711 322)">Ultralight</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 856.422 322)">Thin</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1153.13 322)">Light</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1449.84 322)">Regular</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1746.56 322)">Medium</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2043.27 322)">Semibold</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2339.98 322)">Bold</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2636.69 322)">Heavy</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2933.4 322)">Black</text>
<line style="fill:none;stroke:black;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1903" y2="1903"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 263 1953)">Design Variations</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1971)">Symbols are supported in up to nine weights and three scales.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1989)">For optimal layout with text and other symbols, vertically align</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 2007)">symbols with the adjacent text.</text>
<line style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="776" x2="776" y1="1919" y2="1933"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 776 1953)">Margins</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 1971)">Leading and trailing margins on the left and right side of each symbol</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 1989)">can be adjusted by modifying the x-location of the margin guidelines.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 2007)">Modifications are automatically applied proportionally to all</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 2025)">scales and weights.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 1289 1953)">Exporting</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 1289 1971)">Symbols should be outlined when exporting to ensure the</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 1289 1989)">design is preserved when submitting to Xcode.</text>
<text id="template-version" style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1933)">Template v.5.0</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1951)">Requires Xcode 15 or greater</text>
<text id="descriptive-name" style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1969)">Generated from highlight</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1987)">Typeset at 100.0 points</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 726)">Small</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1156)">Medium</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1586)">Large</text>
</g>
<g id="Guides">
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 696)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-S" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="696" y2="696"/>
<line id="Capline-S" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="625.541" y2="625.541"/>
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 1126)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-M" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1126" y2="1126"/>
<line id="Capline-M" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1055.54" y2="1055.54"/>
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 1556)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-L" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1556" y2="1556"/>
<line id="Capline-L" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1485.54" y2="1485.54"/>
<line id="left-margin-Ultralight-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="513.711" x2="513.711" y1="600.785" y2="720.121"/>
<line id="right-margin-Ultralight-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="605.711" x2="605.711" y1="600.785" y2="720.121"/>
<line id="left-margin-Regular-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="1403.84" x2="1403.84" y1="600.785" y2="720.121"/>
<line id="right-margin-Regular-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="1495.84" x2="1495.84" y1="600.785" y2="720.121"/>
<line id="left-margin-Black-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="2887.4" x2="2887.4" y1="600.785" y2="720.121"/>
<line id="right-margin-Black-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="2979.4" x2="2979.4" y1="600.785" y2="720.121"/>
</g>
<g id="Symbols">
<g id="Ultralight-S" transform="matrix(1 0 0 1 513.711 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="highlight">
<path id="Union" fill-rule="evenodd" clip-rule="evenodd" d="M61.6845 13.7912C66.2528 9.22278 73.6612 9.22422 78.2269 13.7949C82.7858 18.3594 82.7888 25.7563 78.2307 30.3224L70.5528 38.0078C75.7169 44.3454 79.2633 50.7364 80.4805 56.6616C81.8307 63.2364 80.2981 69.3607 74.9289 73.702C70.5466 77.2448 65.6072 76.8122 61.5197 75.1058C57.5196 73.4357 53.9245 70.4193 51.4123 67.967C50.2767 66.858 50.2553 65.0377 51.3637 63.9015C52.4728 62.7661 54.2931 62.7441 55.4291 63.8529C57.7597 66.128 60.7357 68.5449 63.7359 69.7975C66.6485 71.0135 69.14 70.9916 71.3165 69.2323C74.7018 66.4945 75.8548 62.7104 74.8503 57.8184C73.9125 53.2533 71.0841 47.8652 66.4686 42.1032L59.1389 49.4479C56.4425 52.1488 52.7811 53.6668 48.9641 53.6668H41.2076C39.6203 53.6664 38.3329 52.3791 38.3326 50.7918V43.099C38.3326 39.287 39.8487 35.6274 42.544 32.9317L49.6641 25.8078C44.1411 22.6262 38.2906 20.9751 32.9681 20.8252C26.855 20.6535 21.6374 22.4543 18.2113 25.9164C14.0513 30.1214 12.5173 36.6537 13.9213 44.222C15.3218 51.7705 19.617 60.0325 26.5967 67.0873C34.8839 75.4627 42.5044 77.7526 46.149 78.6734C47.6868 79.063 48.6183 80.6239 48.2304 82.1623C47.8411 83.7013 46.2769 84.6363 44.7377 84.2474C40.4702 83.1692 31.7768 80.4931 22.5126 71.1302C14.8232 63.3586 9.90415 54.0856 8.2686 45.2702C6.63711 36.4751 8.24726 27.8129 14.1234 21.8734C18.9441 17.0013 25.8655 14.8745 33.1329 15.079C39.9278 15.2707 47.2042 17.4927 53.8531 21.6188L61.6845 13.7912ZM74.1578 17.8604C71.8372 15.537 68.072 15.5344 65.7499 17.8566L46.6094 36.9971C44.9923 38.6146 44.0826 40.8121 44.0826 43.099V47.9168H48.9641C51.2537 47.9168 53.4515 47.007 55.0697 45.3863L74.1615 26.257C76.4767 23.9362 76.4747 20.1801 74.1578 17.8604Z" fill="black"/>
</g>
</svg>

</g>
<g id="Regular-S" transform="matrix(1 0 0 1 1403.84 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="highlight">
<path id="Union" fill-rule="evenodd" clip-rule="evenodd" d="M61.6845 13.7912C66.2528 9.22278 73.6612 9.22422 78.2269 13.7949C82.7858 18.3594 82.7888 25.7563 78.2307 30.3224L70.5528 38.0078C75.7169 44.3454 79.2633 50.7364 80.4805 56.6616C81.8307 63.2364 80.2981 69.3607 74.9289 73.702C70.5466 77.2448 65.6072 76.8122 61.5197 75.1058C57.5196 73.4357 53.9245 70.4193 51.4123 67.967C50.2767 66.858 50.2553 65.0377 51.3637 63.9015C52.4728 62.7661 54.2931 62.7441 55.4291 63.8529C57.7597 66.128 60.7357 68.5449 63.7359 69.7975C66.6485 71.0135 69.14 70.9916 71.3165 69.2323C74.7018 66.4945 75.8548 62.7104 74.8503 57.8184C73.9125 53.2533 71.0841 47.8652 66.4686 42.1032L59.1389 49.4479C56.4425 52.1488 52.7811 53.6668 48.9641 53.6668H41.2076C39.6203 53.6664 38.3329 52.3791 38.3326 50.7918V43.099C38.3326 39.287 39.8487 35.6274 42.544 32.9317L49.6641 25.8078C44.1411 22.6262 38.2906 20.9751 32.9681 20.8252C26.855 20.6535 21.6374 22.4543 18.2113 25.9164C14.0513 30.1214 12.5173 36.6537 13.9213 44.222C15.3218 51.7705 19.617 60.0325 26.5967 67.0873C34.8839 75.4627 42.5044 77.7526 46.149 78.6734C47.6868 79.063 48.6183 80.6239 48.2304 82.1623C47.8411 83.7013 46.2769 84.6363 44.7377 84.2474C40.4702 83.1692 31.7768 80.4931 22.5126 71.1302C14.8232 63.3586 9.90415 54.0856 8.2686 45.2702C6.63711 36.4751 8.24726 27.8129 14.1234 21.8734C18.9441 17.0013 25.8655 14.8745 33.1329 15.079C39.9278 15.2707 47.2042 17.4927 53.8531 21.6188L61.6845 13.7912ZM74.1578 17.8604C71.8372 15.537 68.072 15.5344 65.7499 17.8566L46.6094 36.9971C44.9923 38.6146 44.0826 40.8121 44.0826 43.099V47.9168H48.9641C51.2537 47.9168 53.4515 47.007 55.0697 45.3863L74.1615 26.257C76.4767 23.9362 76.4747 20.1801 74.1578 17.8604Z" fill="black"/>
</g>
</svg>

</g>
<g id="Black-S" transform="matrix(1 0 0 1 2887.4 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="highlight">
<path id="Union" fill-rule="evenodd" clip-rule="evenodd" d="M61.6845 13.7912C66.2528 9.22278 73.6612 9.22422 78.2269 13.7949C82.7858 18.3594 82.7888 25.7563 78.2307 30.3224L70.5528 38.0078C75.7169 44.3454 79.2633 50.7364 80.4805 56.6616C81.8307 63.2364 80.2981 69.3607 74.9289 73.702C70.5466 77.2448 65.6072 76.8122 61.5197 75.1058C57.5196 73.4357 53.9245 70.4193 51.4123 67.967C50.2767 66.858 50.2553 65.0377 51.3637 63.9015C52.4728 62.7661 54.2931 62.7441 55.4291 63.8529C57.7597 66.128 60.7357 68.5449 63.7359 69.7975C66.6485 71.0135 69.14 70.9916 71.3165 69.2323C74.7018 66.4945 75.8548 62.7104 74.8503 57.8184C73.9125 53.2533 71.0841 47.8652 66.4686 42.1032L59.1389 49.4479C56.4425 52.1488 52.7811 53.6668 48.9641 53.6668H41.2076C39.6203 53.6664 38.3329 52.3791 38.3326 50.7918V43.099C38.3326 39.287 39.8487 35.6274 42.544 32.9317L49.6641 25.8078C44.1411 22.6262 38.2906 20.9751 32.9681 20.8252C26.855 20.6535 21.6374 22.4543 18.2113 25.9164C14.0513 30.1214 12.5173 36.6537 13.9213 44.222C15.3218 51.7705 19.617 60.0325 26.5967 67.0873C34.8839 75.4627 42.5044 77.7526 46.149 78.6734C47.6868 79.063 48.6183 80.6239 48.2304 82.1623C47.8411 83.7013 46.2769 84.6363 44.7377 84.2474C40.4702 83.1692 31.7768 80.4931 22.5126 71.1302C14.8232 63.3586 9.90415 54.0856 8.2686 45.2702C6.63711 36.4751 8.24726 27.8129 14.1234 21.8734C18.9441 17.0013 25.8655 14.8745 33.1329 15.079C39.9278 15.2707 47.2042 17.4927 53.8531 21.6188L61.6845 13.7912ZM74.1578 17.8604C71.8372 15.537 68.072 15.5344 65.7499 17.8566L46.6094 36.9971C44.9923 38.6146 44.0826 40.8121 44.0826 43.099V47.9168H48.9641C51.2537 47.9168 53.4515 47.007 55.0697 45.3863L74.1615 26.257C76.4767 23.9362 76.4747 20.1801 74.1578 17.8604Z" fill="black"/>
</g>
</svg>

</g>
</g>
</svg>