//
//  CurrencyConversionView.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//

import SwiftData
import SwiftUI

/// 货币转换视图
///
/// 在创建交易时，当账单货币与卡片货币不同时，使用此视图进行货币转换。
/// 支持手动输入金额和汇率，或通过系统汇率自动计算。
///
/// ## 功能特性
/// - 支持多种交易类型
/// - 实时汇率计算
/// - 数字键盘输入
/// - 货币选择器
struct CurrencyConversionView: View {
  // MARK: - 环境属性

  @Environment(\.dismiss) private var dismiss
  @Environment(\.modelContext) private var modelContext
  @Environment(\.dataManager) private var dataManager

  // MARK: - 视图模型

  /// 使用状态对象来处理状态更新
  @StateObject private var viewModel = CurrencyConversionVM()

  // MARK: - 绑定属性

  /// 本位币代码
  @Binding var baseCurrencyCode: String
  /// 支出货币代码
  @Binding var expenseCurrencyCode: String
  /// 收入货币代码
  @Binding var incomeCurrencyCode: String
  /// 转账货币代码
  @Binding var transferCurrencyCode: String
  /// 交易金额
  @Binding var transactionAmount: String
  /// 视图显示状态
  @Binding var isSheetVisible: Bool
  /// 转换货币代码
  @Binding var convertCurrencyCode: String
  /// 转换汇率
  @Binding var conversionRate: String

  // MARK: - 属性

  /// 交易类型
  var transactionType: TransactionType

  /// 这些变量用于接收初始值
  var initialBillCurrencyCode: String = ""

  // MARK: - 状态属性

  /// 账单货币代码
  @State private var billCurrencyCode: String = ""
  /// 账单货币符号
  @State private var billCurrencyCodeSymbol: String = ""
  /// 转换货币选择器显示状态
  @State private var isConvertCurrencyPickerVisible: Bool = false
  /// 账单货币选择器显示状态
  @State private var isBillCurrencyPickerVisible: Bool = false

  // MARK: - 计算属性

  /// 获取卡片货币代码
  private var cardCurrencyCode: String {
    switch transactionType {
    case .expense:
      return expenseCurrencyCode
    case .income:
      return incomeCurrencyCode
    case .transfer:
      return transferCurrencyCode
    case .refund, .createCard, .adjustCard:
      return expenseCurrencyCode  // 默认使用支出货币代码
    }
  }

  // MARK: - 主体视图

  var body: some View {
    VStack(spacing: 12) {
      // 头部
      HStack {
        // 左侧关闭按钮
        Button(action: {
          dataManager.hapticManager.trigger(.impactLight)
          isSheetVisible = false
        }) {
          Image("circle-x")
            .foregroundColor(.cBlack.opacity(0.2))
            .font(.system(size: 18, weight: .medium))
        }

        Spacer()

        // 中间标题
        Text("货币换算")
          .font(.system(size: 18, weight: .medium))
          .foregroundColor(.cBlack)

        Spacer()

        // 右侧完成按钮
        Button(action: {
          dataManager.hapticManager.trigger(.impactMedium)
          saveAndDismiss()
        }) {
          Text("完成")
            .font(.system(size: 16, weight: .medium))
            .foregroundColor(.cAccentBlue)
        }
      }
      .padding(.horizontal, 16)
      .padding(.top, 12)

      // 主要内容区域

      VStack(spacing: 12) {
        // 货币转换卡片
        conversionCardView

        // 汇率显示
        HStack(spacing: 0) {

          Text("1 \(billCurrencyCode)   =   ")
            .font(.system(size: 14, weight: .medium))
            .foregroundColor(.cBlack.opacity(0.8))

          Button(action: {
            dataManager.hapticManager.trigger(.impactLight)
            viewModel.activeField = .conversionRate
            viewModel.tempAmount =
              viewModel.conversionRate.isEmpty ? "1.0" : viewModel.conversionRate
            viewModel.isNumericKeypadVisible = true
          }) {
            Text(
              "\(viewModel.conversionRate.isEmpty ? "1.0" : viewModel.conversionRate) \(viewModel.convertCurrencyCode)"
            )
            .font(.system(size: 14, weight: .semibold))
            .foregroundColor(.cAccentBlue)
            .underline()
          }
          Spacer()
          Button(action: {
            dataManager.hapticManager.trigger(.impactLight)
            withAnimation(.easeInOut(duration: 0.2)) {
              updateExchangeRate()
            }
          }) {
            HStack(spacing: 4) {
              Text("还原")
              Image("arrow-undo-up")
            }
            .font(.system(size: 12, weight: .medium))
            .foregroundColor(.cAccentBlue)
          }
        }

        Spacer()
      }
      .padding(.horizontal, 16)
      .padding(.vertical, 12)

    }
    .floatingSheet(
      isPresented: $isBillCurrencyPickerVisible,
      config: SheetBase(
        maxDetent: .fraction(0.7),
        cornerRadius: 24,
        interactiveDimiss: false,
        hPadding: 8,
        bPadding: 4
      )
    ) {
      SelectCurrency(
        selectedCurrencyId: dataManager.currencies.first(where: { $0.code == billCurrencyCode })?
          .id,
        onCurrencySelected: { currency in
          if let currency = currency {
            billCurrencyCode = currency.code
            billCurrencyCodeSymbol = currency.symbol
            updateExchangeRate()
          }
          isBillCurrencyPickerVisible = false
        }
      )
    }
    .floatingSheet(
      isPresented: $isConvertCurrencyPickerVisible,
      config: SheetBase(
        maxDetent: .height(300),
        cornerRadius: 24,
        interactiveDimiss: false,
        hPadding: 8,
        bPadding: 4
      )
    ) {
      SelectCurrency(
        selectedCurrencyId: dataManager.currencies.first(where: {
          $0.code == viewModel.convertCurrencyCode
        })?.id,
        onCurrencySelected: { currency in
          if let currency = currency {
            viewModel.convertCurrencyCode = currency.code
            viewModel.convertCurrencyCodeSymbol = currency.symbol
            updateExchangeRate()
          }
          isConvertCurrencyPickerVisible = false
        }
      )
    }
    .floatingSheet(
      isPresented: $viewModel.isNumericKeypadVisible,
      config: SheetBase(
        maxDetent: .height(314),
        cornerRadius: 24,
        interactiveDimiss: false,
        hPadding: 8,
        bPadding: 4
      )
    ) {
      NumericKeypad(
        text: $viewModel.tempAmount,
        onSave: {
          viewModel.saveNumericInput()
        },
        allowNegative: false,
        maxDecimalPlaces: 6  // 汇率支持6位小数
      )
      .background(.cBeige)
    }
    .onAppear {
      // 根据交易类型设置初始值
      setupInitialValues()
    }
    .onChange(of: billCurrencyCode) { updateExchangeRate() }
    .onChange(of: viewModel.convertCurrencyCode) { updateExchangeRate() }
  }

  // 根据交易类型设置初始值
  private func setupInitialValues() {
    // 设置账单金额为当前交易金额
    viewModel.billAmount = transactionAmount

    // 设置初始账单货币代码
    if !initialBillCurrencyCode.isEmpty {
      billCurrencyCode = initialBillCurrencyCode
    } else {
      switch transactionType {
      case .expense:
        billCurrencyCode = expenseCurrencyCode
      case .income:
        billCurrencyCode = incomeCurrencyCode
      case .transfer:
        billCurrencyCode = transferCurrencyCode
      case .refund, .createCard, .adjustCard:
        billCurrencyCode = expenseCurrencyCode  // 默认使用支出货币代码
      }
    }

    // 设置账单货币符号
    if let currency = dataManager.currencies.first(where: {
      $0.code == billCurrencyCode
    }) {
      billCurrencyCodeSymbol = currency.symbol
    }

    // 始终设置初始换算货币为本位币
    viewModel.convertCurrencyCode = baseCurrencyCode

    // 设置换算货币符号
    if let currency = dataManager.currencies.first(where: {
      $0.code == viewModel.convertCurrencyCode
    }) {
      viewModel.convertCurrencyCodeSymbol = currency.symbol
    }

    // 更新汇率
    updateExchangeRate()
  }

  // MARK: - UI Components

  /// 货币转换卡片视图
  private var conversionCardView: some View {
    VStack(spacing: 12) {
      // 账单货币输入
      currencyAmountRow(
        title: "账单金额",
        currencyCode: billCurrencyCode,
        currencySymbol: billCurrencyCodeSymbol,
        amount: viewModel.billAmount.isEmpty ? "0" : viewModel.billAmount,
        onSelectCurrency: { isBillCurrencyPickerVisible = true },
        onSelectAmount: {
          viewModel.activeField = .billAmount
          viewModel.tempAmount = viewModel.billAmount.isEmpty ? "0" : viewModel.billAmount
          viewModel.isNumericKeypadVisible = true
        }
      )

      // 转换箭头
      Image("arrow-down")
        .font(.system(size: 16, weight: .medium))
        .foregroundColor(.cAccentBlue)

      // 换算结果
      currencyAmountRow(
        title: "换算金额",
        currencyCode: viewModel.convertCurrencyCode,
        currencySymbol: dataManager.currencies.first { $0.code == viewModel.convertCurrencyCode }?
          .symbol ?? "¥",
        amount: viewModel.convertAmount.isEmpty
          ? viewModel.calculateConvertAmount() : viewModel.convertAmount,
        onSelectCurrency: { isConvertCurrencyPickerVisible = true },
        onSelectAmount: {
          viewModel.activeField = .convertAmount
          viewModel.tempAmount = viewModel.convertAmount.isEmpty ? "0" : viewModel.convertAmount
          viewModel.isNumericKeypadVisible = true
        }
      )
    }

  }

  /// 货币金额输入行
  private func currencyAmountRow(
    title: String,
    currencyCode: String,
    currencySymbol: String,
    amount: String,
    onSelectCurrency: @escaping () -> Void,
    onSelectAmount: @escaping () -> Void
  ) -> some View {
    VStack(spacing: 12) {

      // 货币和金额输入区域
      HStack(spacing: 12) {
        // 货币选择按钮
        Button(action: {
          dataManager.hapticManager.trigger(.impactLight)
          onSelectCurrency()
        }) {
          HStack(spacing: 8) {
            Text(currencySymbol)
              .font(.system(size: 18, weight: .semibold))
              .foregroundColor(.cBlack)

            Text(currencyCode)
              .font(.system(size: 14, weight: .medium))
              .foregroundColor(.cBlack.opacity(0.7))

            Image("chevron-bottom")
              .font(.system(size: 12, weight: .medium))
              .foregroundColor(.cBlack.opacity(0.5))
          }
          .padding(.horizontal, 16)
          .padding(.vertical, 12)
          .background(.cBeige.opacity(0.3))
          .cornerRadius(12)
        }

        // 金额输入按钮
        Button(action: {
          dataManager.hapticManager.trigger(.impactLight)
          onSelectAmount()
        }) {
          HStack {
            Text(title)
              .font(.system(size: 14, weight: .medium))
              .foregroundColor(.cBlack.opacity(0.3))
            Spacer()
            Text(amount)
              .font(.system(size: 20, weight: .semibold))
              .foregroundColor(.cBlack)
              .multilineTextAlignment(.trailing)
          }
          .padding(.horizontal, 16)
          .padding(.vertical, 12)
          .background(.cBeige.opacity(0.3))
          .cornerRadius(12)
        }
      }
    }
  }

  private func amountButton(amount: String, onTap: @escaping () -> Void) -> some View {
    Button(action: onTap) {
      Text(amount)
        .font(.system(size: 15, weight: .regular))
        .foregroundColor(.cBlack)
        .frame(maxWidth: .infinity, alignment: .trailing)
        .padding(.horizontal, 16)
    }
    .frame(height: 56)
    .background(.cWhite.opacity(0.5))
    .cornerRadius(24)
    .overlay(
      RoundedRectangle(cornerRadius: 24)
        .strokeBorder(.cAccentBlue.opacity(0.08), lineWidth: 1)
    )
  }

  // MARK: - Exchange Rate Logic

  private func updateExchangeRate() {
    guard !billCurrencyCode.isEmpty && !viewModel.convertCurrencyCode.isEmpty else { return }

    guard let sourceCurrency = dataManager.currencies.first(where: { $0.code == billCurrencyCode }),
      let targetCurrency = dataManager.currencies.first(where: {
        $0.code == viewModel.convertCurrencyCode
      })
    else { return }

    // 计算并设置汇率
    if sourceCurrency.code == targetCurrency.code {
      viewModel.conversionRate = "1"
    } else if sourceCurrency.code == baseCurrencyCode {
      let rate = 1.0 / targetCurrency.rate
      viewModel.conversionRate = viewModel.formatNumberString(rate, precision: 6)
    } else if targetCurrency.code == baseCurrencyCode {
      viewModel.conversionRate = viewModel.formatNumberString(sourceCurrency.rate, precision: 6)
    } else {
      let rate = sourceCurrency.rate / targetCurrency.rate
      viewModel.conversionRate = viewModel.formatNumberString(rate, precision: 6)
    }

    // 更新换算金额（如果已有账单金额）
    if !viewModel.billAmount.isEmpty && viewModel.billAmount != "0" {
      viewModel.updateConvertAmount()
    }
  }

  // MARK: - Save and Dismiss

  private func saveAndDismiss() {
    // 更新交易金额
    if !viewModel.billAmount.isEmpty && viewModel.billAmount != "0" {
      transactionAmount = viewModel.billAmount
    }

    // 根据交易类型更新对应的货币代码
    switch transactionType {
    case .expense:
      expenseCurrencyCode = billCurrencyCode
    case .income:
      incomeCurrencyCode = billCurrencyCode
    case .transfer:
      transferCurrencyCode = billCurrencyCode
    case .refund, .createCard, .adjustCard:
      expenseCurrencyCode = billCurrencyCode  // 默认更新支出货币代码
    }

    // 更新换算货币代码
    convertCurrencyCode = viewModel.convertCurrencyCode
    conversionRate = viewModel.conversionRate

    // 关闭货币转换视图
    isSheetVisible = false
  }
}

// MARK: - 预览代码 (Preview Provider)

#if DEBUG
  struct CurrencyConversionView_Previews: PreviewProvider {
    static var previews: some View {
      CurrencyConversionPreviewContainer()
    }
  }

  struct CurrencyConversionPreviewContainer: View {
    @State private var showSheet = false
    @State private var baseCurrencyCode = "CNY"
    @State private var expenseCurrencyCode = "USD"
    @State private var incomeCurrencyCode = "CNY"
    @State private var transferCurrencyCode = "CNY"
    @State private var transactionAmount = "100"
    @State private var isSheetVisible = true
    @State private var convertCurrencyCode = "CNY"
    @State private var conversionRate = "7.2"

    var body: some View {
      VStack {
        Button("显示货币转换") {
          showSheet = true
        }
        .padding()
        .background(.cAccentBlue)
        .foregroundColor(.white)
        .cornerRadius(12)
      }
      .frame(maxWidth: .infinity, maxHeight: .infinity)
      .background(.cLightBlue)
      .floatingSheet(
        isPresented: $showSheet,
        config: SheetBase(
          maxDetent: .fraction(0.9),
          cornerRadius: 24,
          interactiveDimiss: true,
          hPadding: 8,
          bPadding: 4
        )
      ) {
        CurrencyConversionView(
          baseCurrencyCode: $baseCurrencyCode,
          expenseCurrencyCode: $expenseCurrencyCode,
          incomeCurrencyCode: $incomeCurrencyCode,
          transferCurrencyCode: $transferCurrencyCode,
          transactionAmount: $transactionAmount,
          isSheetVisible: $isSheetVisible,
          convertCurrencyCode: $convertCurrencyCode,
          conversionRate: $conversionRate,
          transactionType: .expense
        )
        .environment(\.dataManager, DataManagement())
      }
    }
  }
#endif
