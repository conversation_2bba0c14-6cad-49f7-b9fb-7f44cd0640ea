<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg
PUBLIC "-//W3C//DTD SVG 1.1//EN"
     "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
     <!--Created with SF Symbol Generator (v1.0.0)-->

<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="3300" height="2200">
<!--glyph: "script.medium", point size: 100.0-->
<style>.SFSymbolsPreviewWireframe {fill:none;opacity:1.0;stroke:black;stroke-width:0.5}
</style>
<g id="Notes">
<rect height="2200" id="artboard" style="fill:white;opacity:1" width="3300" x="0" y="0"/>
<line style="fill:none;stroke:black;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="292" y2="292"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 263 322)">Weight/Scale Variations</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 559.711 322)">Ultralight</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 856.422 322)">Thin</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1153.13 322)">Light</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1449.84 322)">Regular</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1746.56 322)">Medium</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2043.27 322)">Semibold</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2339.98 322)">Bold</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2636.69 322)">Heavy</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2933.4 322)">Black</text>
<line style="fill:none;stroke:black;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1903" y2="1903"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 263 1953)">Design Variations</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1971)">Symbols are supported in up to nine weights and three scales.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1989)">For optimal layout with text and other symbols, vertically align</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 2007)">symbols with the adjacent text.</text>
<line style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="776" x2="776" y1="1919" y2="1933"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 776 1953)">Margins</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 1971)">Leading and trailing margins on the left and right side of each symbol</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 1989)">can be adjusted by modifying the x-location of the margin guidelines.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 2007)">Modifications are automatically applied proportionally to all</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 2025)">scales and weights.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 1289 1953)">Exporting</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 1289 1971)">Symbols should be outlined when exporting to ensure the</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 1289 1989)">design is preserved when submitting to Xcode.</text>
<text id="template-version" style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1933)">Template v.5.0</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1951)">Requires Xcode 15 or greater</text>
<text id="descriptive-name" style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1969)">Generated from script</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1987)">Typeset at 100.0 points</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 726)">Small</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1156)">Medium</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1586)">Large</text>
</g>
<g id="Guides">
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 696)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-S" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="696" y2="696"/>
<line id="Capline-S" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="625.541" y2="625.541"/>
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 1126)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-M" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1126" y2="1126"/>
<line id="Capline-M" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1055.54" y2="1055.54"/>
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 1556)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-L" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1556" y2="1556"/>
<line id="Capline-L" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1485.54" y2="1485.54"/>
<line id="left-margin-Ultralight-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="513.711" x2="513.711" y1="600.785" y2="720.121"/>
<line id="right-margin-Ultralight-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="605.711" x2="605.711" y1="600.785" y2="720.121"/>
<line id="left-margin-Regular-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="1403.84" x2="1403.84" y1="600.785" y2="720.121"/>
<line id="right-margin-Regular-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="1495.84" x2="1495.84" y1="600.785" y2="720.121"/>
<line id="left-margin-Black-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="2887.4" x2="2887.4" y1="600.785" y2="720.121"/>
<line id="right-margin-Black-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="2979.4" x2="2979.4" y1="600.785" y2="720.121"/>
</g>
<g id="Symbols">
<g id="Ultralight-S" transform="matrix(1 0 0 1 513.711 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="script">
<g id="Union">
<path d="M46.9583 38.3333C48.5462 38.3333 49.8333 39.6205 49.8333 41.2083C49.8333 42.7962 48.5462 44.0833 46.9583 44.0833H41.2083C39.6205 44.0833 38.3333 42.7962 38.3333 41.2083C38.3333 39.6205 39.6205 38.3333 41.2083 38.3333H46.9583Z" fill="black"/>
<path d="M50.7917 26.8333C52.3795 26.8333 53.6667 28.1205 53.6667 29.7083C53.6667 31.2962 52.3795 32.5833 50.7917 32.5833H41.2083C39.6205 32.5833 38.3333 31.2962 38.3333 29.7083C38.3333 28.1205 39.6205 26.8333 41.2083 26.8333H50.7917Z" fill="black"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M73.7917 11.5C79.5551 11.5 84.3333 16.2781 84.3333 22.0417V27.7917C84.3333 33.6139 79.6135 38.3333 73.7917 38.3333H69V69.9658C69 75.7818 64.2818 80.5 58.4658 80.5H22.1689C19.3405 80.5 16.6267 79.3773 14.6258 77.3779C12.6244 75.3771 11.5003 72.662 11.5 69.8348V68.0417C11.5002 62.2196 16.2212 57.5009 22.0417 57.5H23V22.0304C23.0003 19.237 24.1109 16.556 26.0921 14.5809C28.0703 12.6063 30.7511 11.5041 33.5417 11.5037L73.7917 11.5ZM22.0417 63.25C19.3955 63.2509 17.2502 65.3966 17.25 68.0417V69.8348C17.2503 71.1384 17.7697 72.3875 18.6912 73.3088C19.6137 74.2307 20.8643 74.75 22.1689 74.75H49.0771C48.342 73.313 47.9167 71.6903 47.9167 69.9658V68.0417C47.9167 65.3965 45.7712 63.2508 43.125 63.25H22.0417ZM33.5417 17.2537C32.2667 17.2541 31.0487 17.7568 30.1538 18.6501L30.1501 18.6538C29.2534 19.5483 28.7503 20.7632 28.75 22.0304V57.5H43.125C48.9452 57.5008 53.6667 62.2192 53.6667 68.0417V69.9658C53.6667 72.6062 55.8105 74.75 58.4508 74.75H58.4658C61.1062 74.75 63.25 72.6062 63.25 69.9658V22.0417C63.25 20.3599 63.6598 18.7238 64.4105 17.2537H33.5417ZM73.7917 17.2537H73.7131C72.4707 17.2741 71.2834 17.7741 70.4038 18.6538C69.5053 19.5528 69 20.7712 69 22.0417V32.5833H73.7917C76.4384 32.5833 78.5833 30.4378 78.5833 27.7917V22.0417C78.5833 19.4538 76.3795 17.2537 73.7917 17.2537Z" fill="black"/>
</g>
</g>
</svg>

</g>
<g id="Regular-S" transform="matrix(1 0 0 1 1403.84 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="script">
<g id="Union">
<path d="M46.9583 38.3333C48.5462 38.3333 49.8333 39.6205 49.8333 41.2083C49.8333 42.7962 48.5462 44.0833 46.9583 44.0833H41.2083C39.6205 44.0833 38.3333 42.7962 38.3333 41.2083C38.3333 39.6205 39.6205 38.3333 41.2083 38.3333H46.9583Z" fill="black"/>
<path d="M50.7917 26.8333C52.3795 26.8333 53.6667 28.1205 53.6667 29.7083C53.6667 31.2962 52.3795 32.5833 50.7917 32.5833H41.2083C39.6205 32.5833 38.3333 31.2962 38.3333 29.7083C38.3333 28.1205 39.6205 26.8333 41.2083 26.8333H50.7917Z" fill="black"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M73.7917 11.5C79.5551 11.5 84.3333 16.2781 84.3333 22.0417V27.7917C84.3333 33.6139 79.6135 38.3333 73.7917 38.3333H69V69.9658C69 75.7818 64.2818 80.5 58.4658 80.5H22.1689C19.3405 80.5 16.6267 79.3773 14.6258 77.3779C12.6244 75.3771 11.5003 72.662 11.5 69.8348V68.0417C11.5002 62.2196 16.2212 57.5009 22.0417 57.5H23V22.0304C23.0003 19.237 24.1109 16.556 26.0921 14.5809C28.0703 12.6063 30.7511 11.5041 33.5417 11.5037L73.7917 11.5ZM22.0417 63.25C19.3955 63.2509 17.2502 65.3966 17.25 68.0417V69.8348C17.2503 71.1384 17.7697 72.3875 18.6912 73.3088C19.6137 74.2307 20.8643 74.75 22.1689 74.75H49.0771C48.342 73.313 47.9167 71.6903 47.9167 69.9658V68.0417C47.9167 65.3965 45.7712 63.2508 43.125 63.25H22.0417ZM33.5417 17.2537C32.2667 17.2541 31.0487 17.7568 30.1538 18.6501L30.1501 18.6538C29.2534 19.5483 28.7503 20.7632 28.75 22.0304V57.5H43.125C48.9452 57.5008 53.6667 62.2192 53.6667 68.0417V69.9658C53.6667 72.6062 55.8105 74.75 58.4508 74.75H58.4658C61.1062 74.75 63.25 72.6062 63.25 69.9658V22.0417C63.25 20.3599 63.6598 18.7238 64.4105 17.2537H33.5417ZM73.7917 17.2537H73.7131C72.4707 17.2741 71.2834 17.7741 70.4038 18.6538C69.5053 19.5528 69 20.7712 69 22.0417V32.5833H73.7917C76.4384 32.5833 78.5833 30.4378 78.5833 27.7917V22.0417C78.5833 19.4538 76.3795 17.2537 73.7917 17.2537Z" fill="black"/>
</g>
</g>
</svg>

</g>
<g id="Black-S" transform="matrix(1 0 0 1 2887.4 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="script">
<g id="Union">
<path d="M46.9583 38.3333C48.5462 38.3333 49.8333 39.6205 49.8333 41.2083C49.8333 42.7962 48.5462 44.0833 46.9583 44.0833H41.2083C39.6205 44.0833 38.3333 42.7962 38.3333 41.2083C38.3333 39.6205 39.6205 38.3333 41.2083 38.3333H46.9583Z" fill="black"/>
<path d="M50.7917 26.8333C52.3795 26.8333 53.6667 28.1205 53.6667 29.7083C53.6667 31.2962 52.3795 32.5833 50.7917 32.5833H41.2083C39.6205 32.5833 38.3333 31.2962 38.3333 29.7083C38.3333 28.1205 39.6205 26.8333 41.2083 26.8333H50.7917Z" fill="black"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M73.7917 11.5C79.5551 11.5 84.3333 16.2781 84.3333 22.0417V27.7917C84.3333 33.6139 79.6135 38.3333 73.7917 38.3333H69V69.9658C69 75.7818 64.2818 80.5 58.4658 80.5H22.1689C19.3405 80.5 16.6267 79.3773 14.6258 77.3779C12.6244 75.3771 11.5003 72.662 11.5 69.8348V68.0417C11.5002 62.2196 16.2212 57.5009 22.0417 57.5H23V22.0304C23.0003 19.237 24.1109 16.556 26.0921 14.5809C28.0703 12.6063 30.7511 11.5041 33.5417 11.5037L73.7917 11.5ZM22.0417 63.25C19.3955 63.2509 17.2502 65.3966 17.25 68.0417V69.8348C17.2503 71.1384 17.7697 72.3875 18.6912 73.3088C19.6137 74.2307 20.8643 74.75 22.1689 74.75H49.0771C48.342 73.313 47.9167 71.6903 47.9167 69.9658V68.0417C47.9167 65.3965 45.7712 63.2508 43.125 63.25H22.0417ZM33.5417 17.2537C32.2667 17.2541 31.0487 17.7568 30.1538 18.6501L30.1501 18.6538C29.2534 19.5483 28.7503 20.7632 28.75 22.0304V57.5H43.125C48.9452 57.5008 53.6667 62.2192 53.6667 68.0417V69.9658C53.6667 72.6062 55.8105 74.75 58.4508 74.75H58.4658C61.1062 74.75 63.25 72.6062 63.25 69.9658V22.0417C63.25 20.3599 63.6598 18.7238 64.4105 17.2537H33.5417ZM73.7917 17.2537H73.7131C72.4707 17.2741 71.2834 17.7741 70.4038 18.6538C69.5053 19.5528 69 20.7712 69 22.0417V32.5833H73.7917C76.4384 32.5833 78.5833 30.4378 78.5833 27.7917V22.0417C78.5833 19.4538 76.3795 17.2537 73.7917 17.2537Z" fill="black"/>
</g>
</g>
</svg>

</g>
</g>
</svg>