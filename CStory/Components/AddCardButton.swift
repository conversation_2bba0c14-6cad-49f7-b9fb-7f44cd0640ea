//
//  AddCardButton.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//

import SwiftUI

/// 添加卡片按钮组件
///
/// 简化版本，固定样式，减少维护成本。
/// 专门用于在列表中显示添加新卡片的入口按钮。
///
/// ## 使用示例
/// ```swift
/// AddCardButton(
///   action: { pathManager.path.append(NavigationDestination.cardCategoryView) },
///   shouldDismiss: true
/// )
/// ```
struct AddCardButton: View {

  // MARK: - 属性

  /// 点击动作
  private let action: () -> Void
  /// 是否在点击时自动关闭当前 sheet
  private let shouldDismiss: Bool

  // MARK: - 状态

  @Environment(\.dismiss) private var dismiss
  /// 数据管理器
  @Environment(\.dataManager) private var dataManager

  // MARK: - 初始化

  /// 初始化添加卡片按钮
  /// - Parameters:
  ///   - action: 点击动作
  ///   - shouldDismiss: 是否在点击时自动关闭当前 sheet
  init(action: @escaping () -> Void, shouldDismiss: Bool = false) {
    self.action = action
    self.shouldDismiss = shouldDismiss
  }

  // MARK: - 主体视图

  var body: some View {
    Button(action: {
      dataManager.hapticManager.trigger(.impactMedium)
      if shouldDismiss {
        dismiss()
      }
      action()
    }) {
      HStack(spacing: 8) {
        Image("credit-card-add")
          .font(.system(size: 21))
          .frame(width: 24, height: 24)
        Text("添加卡片")
          .font(.system(size: 14, weight: .medium))
      }
      .foregroundStyle(.cBlack.opacity(0.4))
      .frame(maxWidth: .infinity)
      .padding(.vertical, 12)
      .frame(height: 56)
      .background(.cWhite.opacity(0.5))
      .cornerRadius(24)
      .overlay(
        RoundedRectangle(cornerRadius: 24)
          .strokeBorder(
            .cAccentBlue.opacity(0.08),
            style: StrokeStyle(lineWidth: 1, dash: [4])
          )
      )
    }
    .buttonStyle(PlainButtonStyle())
  }
}

// MARK: - 预览

#Preview("基本使用") {
  VStack(spacing: 20) {
    // 标准样式
    AddCardButton(
      action: { print("添加卡片按钮点击") },
      shouldDismiss: true
    )
    .padding()

    // 不关闭sheet的版本
    AddCardButton(
      action: { print("添加卡片按钮点击") },
      shouldDismiss: false
    )
    .padding()
  }
  .background(Color.gray.opacity(0.1))
}

#Preview("在列表中使用") {
  ScrollView {
    LazyVStack(spacing: 12) {
      // 模拟一些卡片行
      ForEach(0..<3) { index in
        RoundedRectangle(cornerRadius: 24)
          .fill(Color.blue.opacity(0.1))
          .frame(height: 60)
          .overlay(
            Text("卡片 \(index + 1)")
              .font(.headline)
          )
      }

      // 添加卡片按钮
      AddCardButton(
        action: { print("添加新卡片") },
        shouldDismiss: false
      )
    }
    .padding(12)
  }
  .background(.cLightBlue)
}
