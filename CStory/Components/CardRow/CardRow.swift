import SwiftUI

/// 银行卡信息行视图组件
///
/// 显示银行卡基本信息的可交互行组件，支持显示银行logo、卡片名称、余额信息等。
struct CardRow: View {

  // MARK: - Properties

  /// 卡片行视图模型
  ///
  /// 管理卡片显示数据和交互逻辑的视图模型实例。
  /// 包含卡片名称、余额、选中状态等所有显示相关信息。
  @ObservedObject var viewModel: CardRowVM

  /// 数据管理器
  @Environment(\.dataManager) private var dataManager

  /// 外部点击回调，优先级高于viewModel.onTap
  let onTap: (() -> Void)?

  // MARK: - 初始化

  init(viewModel: CardRowVM, onTap: (() -> Void)? = nil) {
    self.viewModel = viewModel
    self.onTap = onTap
  }

  // MARK: - Body

  var body: some View {
    Button(action: {
      dataManager.hapticManager.trigger(.selection)
      if let externalOnTap = onTap {
        // 使用外部点击回调（优先级最高）
        externalOnTap()
      } else {
        // 使用ViewModel内部回调
        viewModel.onTap?()
      }
    }) {
      HStack(alignment: .center) {
        // MARK: 左侧内容 (银行logo和卡片信息)
        HStack(spacing: 12) {
          // 银行logo
          IconView(
            viewModel: IconViewVM.optionalImage(
              viewModel.bankLogo,
              size: 44,
              style: IconStyle(
                backgroundColor: .cAccentBlue.opacity(0.1),
                cornerRadius: 12
              )
            )
          )

          VStack(alignment: .leading, spacing: 4) {
            HStack(alignment: .center, spacing: 4) {
              Text(viewModel.cardName)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.cBlack)

              if viewModel.hasTypeTag {
                TagView(
                  text: viewModel.cardTypeText,
                  color: viewModel.isCredit ? .orange : .blue
                )
              }
            }

            Text(viewModel.bankName)
              .font(.system(size: 12, weight: .regular))
              .foregroundColor(.cBlack.opacity(0.6))
          }
        }

        Spacer()

        // MARK: 右侧内容 (余额和附加信息)
        VStack(alignment: .trailing, spacing: 4) {
          // 余额显示
          DisplayCurrencyView.size15(
            symbol: viewModel.currencySymbol,
            amount: viewModel.balance
          )
          .simpleFormat()
          .color(viewModel.balanceColor)

          // 附加信息
          if let additionalInfo = viewModel.additionalInfo {
            Text(additionalInfo)
              .font(.system(size: 12, weight: .regular))
              .foregroundColor(.cBlack.opacity(0.4))
          }
        }
      }
      .padding(.leading, 8)
      .padding(.trailing, 12)
      .frame(height: 56)
      .background(.cWhite.opacity(0.5))
      .cornerRadius(24)
      .overlay(
        RoundedRectangle(cornerRadius: 24)
          .strokeBorder(
            viewModel.isSelected ? .cAccentBlue : .cAccentBlue.opacity(0.08),
            lineWidth: viewModel.isSelected ? 1.5 : 1
          )
          .animation(.easeInOut(duration: 0.2), value: viewModel.isSelected)
      )
      .animation(.easeInOut(duration: 0.2), value: viewModel.isSelected)
    }
  }
}

// MARK: - 私有组件

/// 卡片类型标签视图
///
/// 显示卡片类型标识的小型标签组件。
/// 支持信用卡和储蓄卡等不同类型的颜色区分。
///
/// - Parameters:
///   - text: 标签显示文字
///   - color: 标签背景颜色主题
/// - Returns: 配置好的标签视图
/// - Note: 该组件为私有组件，仅在CardRow内部使用
private func TagView(text: String, color: Color) -> some View {
  Text(text)
    .font(.system(size: 10, weight: .medium))
    .foregroundColor(.white)
    .padding(.horizontal, 4)
    .padding(.vertical, 1)
    .background(color.opacity(0.7))
    .cornerRadius(4)
}

// MARK: - 预览代码 (Preview Provider)

#if DEBUG
  struct CardRow_Previews: PreviewProvider {
    static var previews: some View {
      ScrollView {
        VStack(spacing: 10) {
          Text("卡片行预览").font(.largeTitle).bold().padding()

          // 场景1: 普通储蓄卡
          CardRow(
            viewModel: .init(
              cardName: "招商银行储蓄卡",
              bankName: "招商银行",
              balance: 12580.50,
              currencySymbol: "¥",
              isCredit: false,
              hasTypeTag: true,
              isSelected: false
            ))

          // 场景2: 信用卡
          CardRow(
            viewModel: .init(
              cardName: "工行信用卡",
              bankName: "中国工商银行",
              balance: -2500.00,
              currencySymbol: "¥",
              isCredit: true,
              hasTypeTag: true,
              isSelected: false,
              additionalInfo: "额度: ¥10000"
            ))

          // 场景3: 选中状态的卡片
          CardRow(
            viewModel: .init(
              cardName: "支付宝余额",
              bankName: "支付宝",
              balance: 1024.88,
              currencySymbol: "¥",
              isCredit: false,
              hasTypeTag: false,
              isSelected: true
            ))

          // 场景4: 外币卡片
          CardRow(
            viewModel: .init(
              cardName: "美元储蓄卡",
              bankName: "中国银行",
              balance: 500.25,
              currencySymbol: "$",
              isCredit: false,
              hasTypeTag: true,
              isSelected: false,
              additionalInfo: "外币账户"
            ))

        }
        .padding(.horizontal)
      }
      .background(Color(.systemGroupedBackground))
    }
  }
#endif
