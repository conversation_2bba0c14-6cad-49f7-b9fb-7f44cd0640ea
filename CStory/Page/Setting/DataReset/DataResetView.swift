//
//  DataResetView.swift
//  CStory
//
//  Created by AI Assistant on 2025/8/11.
//

import Foundation
import SwiftData
import SwiftUI

/// 数据重置页面
///
/// 提供数据重置功能，包括清除所有交易记录、卡片信息等。
/// 为了防止误操作，提供了确认机制和详细的说明。
struct DataResetView: View {

  // MARK: - Dependencies

  @Environment(\.dataManager) private var dataManager: DataManagement
  @Environment(\.modelContext) private var modelContext
  @EnvironmentObject private var pathManager: PathManagerHelper

  // MARK: - ViewModel

  @State private var viewModel: DataResetVM?

  // MARK: - Body

  var body: some View {
    ZStack {
      VStack(spacing: 0) {
        // MARK: 导航栏
        NavigationBarKit(
          title: "重新来过",
          onBack: {
            dataManager.hapticManager.trigger(.impactLight)
            pathManager.path.removeLast()
          }
        )

        // MARK: 主要内容
        ScrollView {
          VStack(spacing: 24) {
            // 说明区域
            infoSection

            // 底部留出空间给浮动按钮
            Spacer(minLength: 80)
          }
          .padding(.horizontal, 16)
          .padding(.top, 24)
        }
        .background(.cLightBlue)
      }

      // MARK: 底部浮动重置按钮
      FloatingActionButtonView(
        title: (viewModel?.isResetting == true) ? "重置中..." : "重置所有数据",
        action: {
          dataManager.hapticManager.trigger(.warning)
          viewModel?.showResetConfirmation()
        },
        style: .destructive,
        isEnabled: !(viewModel?.isResetting == true)
      )
    }
    .background(.cLightBlue)
    .navigationBarTitleDisplayMode(.inline)
    .navigationBarBackButtonHidden(true)
    .onAppear {
      // 初始化viewModel
      if viewModel == nil {
        viewModel = DataResetVM(modelContext: modelContext, dataManager: dataManager)
      }
    }
    .alert(
      "确认重置",
      isPresented: Binding(
        get: { viewModel?.showingResetConfirmation ?? false },
        set: { viewModel?.showingResetConfirmation = $0 }
      )
    ) {
      Button("取消", role: .cancel) {}
      Button("确认重置", role: .destructive) {
        viewModel?.performReset()
      }
    } message: {
      Text("此操作将删除所有数据，包括交易记录、卡片信息等，且无法恢复。确定要继续吗？")
    }
    .alert(
      "重置完成",
      isPresented: Binding(
        get: { viewModel?.resetCompleted ?? false },
        set: { viewModel?.resetCompleted = $0 }
      )
    ) {
      Button("确定") {
        // 返回到主页面
        pathManager.path = NavigationPath()
        viewModel?.resetState()
      }
    } message: {
      Text("所有数据已成功清除，应用将返回初始状态。")
    }
    .alert(
      "重置失败",
      isPresented: Binding(
        get: { viewModel?.showingError ?? false },
        set: { viewModel?.showingError = $0 }
      )
    ) {
      Button("确定") {}
    } message: {
      Text(viewModel?.errorMessage ?? "未知错误")
    }
  }

  // MARK: - Subviews

  /// 说明区域
  private var infoSection: some View {
    VStack(alignment: .leading, spacing: 12) {
      HStack {
        Text("关于数据重置")
          .font(.system(size: 14, weight: .medium))
          .foregroundColor(.cBlack)

        Spacer()
      }

      VStack(alignment: .leading, spacing: 8) {
        infoItem("• 将删除所有交易记录、分类、卡片等数据")
        infoItem("• 应用将回到首次安装时的状态")
        infoItem("• 此操作不可逆，请确保已备份重要数据")
        infoItem("• 重置后需要重新设置应用偏好")
      }
    }
    .padding(16)
    .background(.cWhite.opacity(0.5))
    .cornerRadius(24)
    .overlay(
      RoundedRectangle(cornerRadius: 24)
        .strokeBorder(Color.orange.opacity(0.2), lineWidth: 1)
    )
  }

  /// 信息项
  private func infoItem(_ text: String) -> some View {
    Text(text)
      .font(.system(size: 12, weight: .regular))
      .foregroundColor(.cBlack.opacity(0.6))
  }

}

// MARK: - Preview

#Preview {
  DataResetView()
    .environment(\.dataManager, DataManagement())
    .environmentObject(PathManagerHelper())
}
