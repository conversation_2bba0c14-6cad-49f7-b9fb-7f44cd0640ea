<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg
PUBLIC "-//W3C//DTD SVG 1.1//EN"
     "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
     <!--Created with SF Symbol Generator (v1.0.0)-->

<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="3300" height="2200">
<!--glyph: "color-swatch.medium", point size: 100.0-->
<style>.SFSymbolsPreviewWireframe {fill:none;opacity:1.0;stroke:black;stroke-width:0.5}
</style>
<g id="Notes">
<rect height="2200" id="artboard" style="fill:white;opacity:1" width="3300" x="0" y="0"/>
<line style="fill:none;stroke:black;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="292" y2="292"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 263 322)">Weight/Scale Variations</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 559.711 322)">Ultralight</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 856.422 322)">Thin</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1153.13 322)">Light</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1449.84 322)">Regular</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1746.56 322)">Medium</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2043.27 322)">Semibold</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2339.98 322)">Bold</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2636.69 322)">Heavy</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2933.4 322)">Black</text>
<line style="fill:none;stroke:black;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1903" y2="1903"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 263 1953)">Design Variations</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1971)">Symbols are supported in up to nine weights and three scales.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1989)">For optimal layout with text and other symbols, vertically align</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 2007)">symbols with the adjacent text.</text>
<line style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="776" x2="776" y1="1919" y2="1933"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 776 1953)">Margins</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 1971)">Leading and trailing margins on the left and right side of each symbol</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 1989)">can be adjusted by modifying the x-location of the margin guidelines.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 2007)">Modifications are automatically applied proportionally to all</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 2025)">scales and weights.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 1289 1953)">Exporting</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 1289 1971)">Symbols should be outlined when exporting to ensure the</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 1289 1989)">design is preserved when submitting to Xcode.</text>
<text id="template-version" style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1933)">Template v.5.0</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1951)">Requires Xcode 15 or greater</text>
<text id="descriptive-name" style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1969)">Generated from color-swatch</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1987)">Typeset at 100.0 points</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 726)">Small</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1156)">Medium</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1586)">Large</text>
</g>
<g id="Guides">
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 696)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-S" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="696" y2="696"/>
<line id="Capline-S" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="625.541" y2="625.541"/>
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 1126)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-M" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1126" y2="1126"/>
<line id="Capline-M" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1055.54" y2="1055.54"/>
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 1556)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-L" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1556" y2="1556"/>
<line id="Capline-L" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1485.54" y2="1485.54"/>
<line id="left-margin-Ultralight-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="513.711" x2="513.711" y1="600.785" y2="720.121"/>
<line id="right-margin-Ultralight-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="605.711" x2="605.711" y1="600.785" y2="720.121"/>
<line id="left-margin-Regular-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="1403.84" x2="1403.84" y1="600.785" y2="720.121"/>
<line id="right-margin-Regular-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="1495.84" x2="1495.84" y1="600.785" y2="720.121"/>
<line id="left-margin-Black-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="2887.4" x2="2887.4" y1="600.785" y2="720.121"/>
<line id="right-margin-Black-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="2979.4" x2="2979.4" y1="600.785" y2="720.121"/>
</g>
<g id="Symbols">
<g id="Ultralight-S" transform="matrix(1 0 0 1 513.711 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="color-swatch">
<g id="Union">
<path fill-rule="evenodd" clip-rule="evenodd" d="M38.3333 63.25C38.3333 68.5427 34.0427 72.8333 28.75 72.8333C23.4573 72.8333 19.1667 68.5427 19.1667 63.25C19.1667 57.9573 23.4573 53.6667 28.75 53.6667C34.0427 53.6667 38.3333 57.9573 38.3333 63.25ZM32.5833 63.25C32.5833 61.1328 30.8671 59.4167 28.75 59.4167C26.6329 59.4167 24.9167 61.1328 24.9167 63.25C24.9167 65.3671 26.6329 67.0833 28.75 67.0833C30.8671 67.0833 32.5833 65.3671 32.5833 63.25Z" fill="black"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M35.4583 7.66667C38.2478 7.66667 40.858 8.46325 43.0651 9.84163C45.6629 9.7528 48.3184 10.3654 50.7318 11.7583L62.3516 18.4666C64.7665 19.861 66.6283 21.8566 67.8508 24.153C70.1458 25.3752 72.1396 27.2346 73.5334 29.6484L80.2417 41.2682C84.2109 48.143 81.856 56.9329 74.9821 60.9028L39.681 81.2786C36.4933 83.2155 32.7526 84.3333 28.75 84.3333C17.106 84.3333 7.66667 74.8941 7.66667 63.25V22.0417C7.66667 14.1026 14.1026 7.66667 22.0417 7.66667H35.4583ZM69.2546 33.7401C68.9549 35.2354 68.4197 36.7109 67.6149 38.105L49.9344 68.7192L72.1071 55.924C76.2311 53.542 77.6438 48.268 75.2629 44.1432L69.2546 33.7401ZM13.4167 63.25C13.4167 71.7182 20.2816 78.5833 28.75 78.5833C37.2184 78.5833 44.0833 71.7182 44.0833 63.25V22.0417C44.0833 19.1938 42.7055 16.669 40.5682 15.0937C39.138 14.0398 37.3752 13.4167 35.4583 13.4167H22.0417C17.2782 13.4167 13.4167 17.2782 13.4167 22.0417V63.25ZM49.8333 57.4027L62.6361 35.23C64.0597 32.764 64.1295 29.8873 63.0666 27.4548C62.3551 25.8271 61.1364 24.4039 59.4766 23.4455L49.0734 17.4372C49.5624 18.8831 49.8333 20.4305 49.8333 22.0417V57.4027Z" fill="black"/>
</g>
</g>
</svg>

</g>
<g id="Regular-S" transform="matrix(1 0 0 1 1403.84 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="color-swatch">
<g id="Union">
<path fill-rule="evenodd" clip-rule="evenodd" d="M38.3333 63.25C38.3333 68.5427 34.0427 72.8333 28.75 72.8333C23.4573 72.8333 19.1667 68.5427 19.1667 63.25C19.1667 57.9573 23.4573 53.6667 28.75 53.6667C34.0427 53.6667 38.3333 57.9573 38.3333 63.25ZM32.5833 63.25C32.5833 61.1328 30.8671 59.4167 28.75 59.4167C26.6329 59.4167 24.9167 61.1328 24.9167 63.25C24.9167 65.3671 26.6329 67.0833 28.75 67.0833C30.8671 67.0833 32.5833 65.3671 32.5833 63.25Z" fill="black"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M35.4583 7.66667C38.2478 7.66667 40.858 8.46325 43.0651 9.84163C45.6629 9.7528 48.3184 10.3654 50.7318 11.7583L62.3516 18.4666C64.7665 19.861 66.6283 21.8566 67.8508 24.153C70.1458 25.3752 72.1396 27.2346 73.5334 29.6484L80.2417 41.2682C84.2109 48.143 81.856 56.9329 74.9821 60.9028L39.681 81.2786C36.4933 83.2155 32.7526 84.3333 28.75 84.3333C17.106 84.3333 7.66667 74.8941 7.66667 63.25V22.0417C7.66667 14.1026 14.1026 7.66667 22.0417 7.66667H35.4583ZM69.2546 33.7401C68.9549 35.2354 68.4197 36.7109 67.6149 38.105L49.9344 68.7192L72.1071 55.924C76.2311 53.542 77.6438 48.268 75.2629 44.1432L69.2546 33.7401ZM13.4167 63.25C13.4167 71.7182 20.2816 78.5833 28.75 78.5833C37.2184 78.5833 44.0833 71.7182 44.0833 63.25V22.0417C44.0833 19.1938 42.7055 16.669 40.5682 15.0937C39.138 14.0398 37.3752 13.4167 35.4583 13.4167H22.0417C17.2782 13.4167 13.4167 17.2782 13.4167 22.0417V63.25ZM49.8333 57.4027L62.6361 35.23C64.0597 32.764 64.1295 29.8873 63.0666 27.4548C62.3551 25.8271 61.1364 24.4039 59.4766 23.4455L49.0734 17.4372C49.5624 18.8831 49.8333 20.4305 49.8333 22.0417V57.4027Z" fill="black"/>
</g>
</g>
</svg>

</g>
<g id="Black-S" transform="matrix(1 0 0 1 2887.4 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="color-swatch">
<g id="Union">
<path fill-rule="evenodd" clip-rule="evenodd" d="M38.3333 63.25C38.3333 68.5427 34.0427 72.8333 28.75 72.8333C23.4573 72.8333 19.1667 68.5427 19.1667 63.25C19.1667 57.9573 23.4573 53.6667 28.75 53.6667C34.0427 53.6667 38.3333 57.9573 38.3333 63.25ZM32.5833 63.25C32.5833 61.1328 30.8671 59.4167 28.75 59.4167C26.6329 59.4167 24.9167 61.1328 24.9167 63.25C24.9167 65.3671 26.6329 67.0833 28.75 67.0833C30.8671 67.0833 32.5833 65.3671 32.5833 63.25Z" fill="black"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M35.4583 7.66667C38.2478 7.66667 40.858 8.46325 43.0651 9.84163C45.6629 9.7528 48.3184 10.3654 50.7318 11.7583L62.3516 18.4666C64.7665 19.861 66.6283 21.8566 67.8508 24.153C70.1458 25.3752 72.1396 27.2346 73.5334 29.6484L80.2417 41.2682C84.2109 48.143 81.856 56.9329 74.9821 60.9028L39.681 81.2786C36.4933 83.2155 32.7526 84.3333 28.75 84.3333C17.106 84.3333 7.66667 74.8941 7.66667 63.25V22.0417C7.66667 14.1026 14.1026 7.66667 22.0417 7.66667H35.4583ZM69.2546 33.7401C68.9549 35.2354 68.4197 36.7109 67.6149 38.105L49.9344 68.7192L72.1071 55.924C76.2311 53.542 77.6438 48.268 75.2629 44.1432L69.2546 33.7401ZM13.4167 63.25C13.4167 71.7182 20.2816 78.5833 28.75 78.5833C37.2184 78.5833 44.0833 71.7182 44.0833 63.25V22.0417C44.0833 19.1938 42.7055 16.669 40.5682 15.0937C39.138 14.0398 37.3752 13.4167 35.4583 13.4167H22.0417C17.2782 13.4167 13.4167 17.2782 13.4167 22.0417V63.25ZM49.8333 57.4027L62.6361 35.23C64.0597 32.764 64.1295 29.8873 63.0666 27.4548C62.3551 25.8271 61.1364 24.4039 59.4766 23.4455L49.0734 17.4372C49.5624 18.8831 49.8333 20.4305 49.8333 22.0417V57.4027Z" fill="black"/>
</g>
</g>
</svg>

</g>
</g>
</svg>