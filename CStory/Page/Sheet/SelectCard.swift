//
//  SelectCard.swift
//  CStory
//
//  Created by nzue on 2025/8/20.
//

import SwiftUI

/// 选择卡片弹窗组件
/// 专门用于卡片选择的 Sheet 组件，接收一个卡片 UUID 并返回选中的卡片 UUID
struct SelectCard: View {

  // MARK: - Properties

  /// 当前选中的卡片ID（外部传入）
  @State private var selectedCardId: UUID?

  /// 数据管理器
  @Environment(\.dataManager) private var dataManager

  /// 关闭弹窗
  @Environment(\.dismiss) private var dismiss

  /// 选择完成回调
  let onCardSelected: (CardModel?) -> Void

  // MARK: - Initialization

  /// 初始化 SelectCard
  /// - Parameters:
  ///   - selectedCardId: 当前选中的卡片ID
  ///   - onCardSelected: 选择完成回调，返回完整的卡片信息
  init(
    selectedCardId: UUID? = nil,
    onCardSelected: @escaping (CardModel?) -> Void
  ) {
    self._selectedCardId = State(initialValue: selectedCardId)
    self.onCardSelected = onCardSelected
  }

  // MARK: - Body

  var body: some View {
    VStack(spacing: 12.0) {
      // 标题栏
      HStack {
        Text("选择卡片")
          .font(.system(size: 16, weight: .medium))
          .foregroundColor(.cBlack)
        Spacer()
        // 关闭按钮
        Image("circle-x")
          .foregroundColor(.cBlack.opacity(0.2))
          .font(.system(size: 18, weight: .medium))
          .onTapGesture {
            dismiss()
          }
      }
      .padding(.top, 12)
      .padding(.horizontal, 16)
        
      // 卡片列表
      ScrollView {
        LazyVStack(spacing: 12) {
          // 循环显示卡片
          ForEach(dataManager.cards.filter { $0.isSelected }, id: \.id) { card in
            Row(
              imageName: card.cover.isEmpty ? nil : card.cover,
              leftMainText: card.name,
              leftSubText: card.bankName,
              tagText: card.isCredit ? "信用卡" : "储蓄卡",
              rightMainText: formatCardBalance(card),
              selectBorder: card.id == selectedCardId,
              onRowTap: {
                selectedCardId = card.id
                dataManager.hapticManager.trigger(.selection)
                onCardSelected(card)  // 返回完整的卡片信息
                dismiss()
              }
            )
          }
        }
        .padding(.vertical, 12)
        .padding(.horizontal, 16)
      }
    }

  }

  /// 格式化卡片余额
  private func formatCardBalance(_ card: CardModel) -> String {
    let formatter = NumberFormatter()
    formatter.numberStyle = .decimal
    formatter.minimumFractionDigits = 2
    formatter.maximumFractionDigits = 2

    let balanceString = formatter.string(from: NSNumber(value: card.balance)) ?? "0.00"
    return "\(card.symbol)\(balanceString)"
  }
}

#Preview {
  // 创建预览用的卡片数据
  let cardId1 = UUID()
  let cardId2 = UUID()

  let sampleCards = [
    CardModel(
      id: cardId1,
      order: 1,
      isCredit: false,
      isSelected: true,
      name: "招商银行储蓄卡",
      remark: "",
      currency: "CNY",
      symbol: "¥",
      balance: 12580.50,
      credit: 0,
      isStatistics: true,
      cover: "Card_CS_1",
      bankName: "招商银行",
      cardNumber: "1234",
      isFixedDueDay: true,
      createdAt: Date(),
      updatedAt: Date()
    ),
    CardModel(
      id: cardId2,
      order: 2,
      isCredit: true,
      isSelected: true,
      name: "工行信用卡",
      remark: "",
      currency: "CNY",
      symbol: "¥",
      balance: -2500.00,
      credit: 10000.0,
      isStatistics: true,
      cover: "Card_CS_2",
      bankName: "中国工商银行",
      cardNumber: "5678",
      isFixedDueDay: true,
      createdAt: Date(),
      updatedAt: Date()
    ),
  ]

  // 创建预览用的数据管理器
  let previewDataManager = DataManagement(cards: sampleCards)

  VStack(spacing: 12.0) {
    Spacer()
    SelectCard(
      selectedCardId: cardId1,  // 传入第一张卡片的ID，应该显示为选中状态
      onCardSelected: { card in
        print("选择了卡片: \(card?.name ?? "无") (\(card?.bankName ?? "无"))")
      }
    )
    Spacer()
  }
  .background(.cBeige)
  .withDataManager(previewDataManager)
}
