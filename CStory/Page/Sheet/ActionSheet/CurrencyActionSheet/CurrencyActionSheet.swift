//
//  CurrencyActionSheet.swift
//  CStory
//
//  Created by NZUE on 2025/8/8.
//

import SwiftUI

/// 货币操作弹窗
///
/// 提供货币操作功能，支持设为本位币和自定义汇率输入。
/// 使用统一的选项卡片样式，带有操作状态指示。
/// 使用 MVVM 架构，通过 CurrencyActionSheetVM 管理业务逻辑。
struct CurrencyActionSheet: View {

  // MARK: - ViewModel

  /// 视图模型
  @StateObject private var viewModel: CurrencyActionSheetVM

  /// 数据管理器
  @EnvironmentObject private var dataManager: DataManagement

  // MARK: - Initialization

  /// 初始化货币操作弹窗
  /// - Parameters:
  ///   - viewModel: 货币操作弹窗视图模型
  init(viewModel: CurrencyActionSheetVM) {
    self._viewModel = StateObject(wrappedValue: viewModel)
  }

  // MARK: - Body

  var body: some View {
    VStack(spacing: 12) {
      // 标题栏
      SheetTitle(
        title: "货币操作",
        button: "circle-x",
        rightButtonAction: viewModel.handleDismiss
      )

      // 货币信息头部
      VStack(spacing: 8) {
        HStack {
          HStack(spacing: 4) {
            Text(viewModel.currency.name)
              .font(.system(size: 16, weight: .semibold))
              .foregroundColor(.cBlack)
            Spacer()
            Text("\(viewModel.currency.code) · \(viewModel.currency.symbol)")
              .font(.system(size: 14))
              .foregroundColor(.cBlack.opacity(0.6))
          }

          Spacer()

          // 本位币标识
          if viewModel.currency.isBaseCurrency {
            Text("本位币")
              .font(.system(size: 12, weight: .medium))
              .foregroundColor(.cWhite)
              .padding(.vertical, 4)
              .background(.cAccentBlue)
              .cornerRadius(8)
          }
        }

        // 当前汇率显示
        if !viewModel.currency.isBaseCurrency {
          HStack {
            Text("当前汇率")
              .font(.system(size: 14, weight: .medium))
              .foregroundColor(.cBlack)
            Spacer()
            Text(viewModel.currentRateText)
              .font(.system(size: 14, weight: .regular))
              .foregroundColor(.cBlack.opacity(0.6))
          }
        }
      }
      .padding(.horizontal, 16)
      .padding(.vertical, 12)
      .background(.cWhite.opacity(0.5))
      .cornerRadius(24)
      .overlay(
        RoundedRectangle(cornerRadius: 24)
          .strokeBorder(.cAccentBlue.opacity(0.08), lineWidth: 1)
      )
      .padding(.horizontal, 16)

      // 操作选项列表
      VStack(spacing: 12) {
        ForEach(viewModel.operationOptions, id: \.title) { option in
          SheetRow(
            title: option.title,
            icon: option.icon
          ) {
            viewModel.handleOptionTap(option)
          }
        }
      }

      Spacer()
    }
    .floatingSheet(
      isPresented: $viewModel.showNumericKeypad,
      config: SheetBase(
        maxDetent: .height(314),
        cornerRadius: 24,
        interactiveDimiss: false,
        hPadding: 8,
        bPadding: 4
      )
    ) {
      NumericKeypad(
        text: $viewModel.customRateText,
        onSave: {
          viewModel.handleSaveCustomRate()
        },
        allowNegative: false,
        maxDecimalPlaces: 6  // 汇率支持6位小数
      )
      .background(.cBeige)
    }
  }

}

// MARK: - Preview Provider

#if DEBUG
  struct CurrencyActionSheet_Previews: PreviewProvider {
    static var previews: some View {
      CurrencyActionPreviewContainer()
    }
  }

  struct CurrencyActionPreviewContainer: View {
    @State private var showSheet = false

    var body: some View {
      VStack {
        Button("显示货币操作菜单") {
          showSheet = true
        }
        .padding()
        .background(.cAccentBlue)
        .foregroundColor(.white)
        .cornerRadius(12)
      }
      .frame(maxWidth: .infinity, maxHeight: .infinity)
      .background(.cLightBlue)
      .floatingSheet(
        isPresented: $showSheet,
        config: SheetBase(
          maxDetent: .fraction(0.5),
          cornerRadius: 24,
          interactiveDimiss: true,
          hPadding: 8,
          bPadding: 4
        )
      ) {
        CurrencyActionSheet(
          viewModel: CurrencyActionSheetVM(
            currency: Self.createPreviewCurrency(),
            dataManager: DataManagement(),
            dismiss: { showSheet = false },
            onSetAsBaseCurrency: { currency in
              print("设为本位币: \(currency.name)")
            },
            onSaveCustomRate: { currency, rate in
              print("保存自定义汇率: \(currency.name) = \(rate)")
            }
          )
        )
        .environmentObject(DataManagement())
      }
    }

    static func createPreviewCurrency() -> CurrencyModel {
      return CurrencyModel(
        name: "美元",
        code: "USD",
        symbol: "$",
        rate: 7.25,
        defaultRate: 7.25,
        isBaseCurrency: false
      )
    }
  }
#endif
