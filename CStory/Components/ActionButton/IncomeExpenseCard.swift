//
//  IncomeExpenseCard.swift
//  CStory
//
//  Created by 咩咩 on 2025/7/18.
//

import SwiftUI

/// 收入支出卡片组件
///
/// 显示收入和支出的统计信息，适用于卡片详情页面等场景
///
/// ## 使用示例
/// ```swift
/// IncomeExpenseCard(
///     income: 2500.00,
///     expense: 1200.50,
///     currencySymbol: "¥"
/// )
/// ```
struct IncomeExpenseCard: View {

  // MARK: - Properties

  let income: Double
  let expense: Double
  let currencySymbol: String

  // MARK: - 初始化

  init(income: Double, expense: Double, currencySymbol: String = "¥") {
    self.income = income
    self.expense = expense
    self.currencySymbol = currencySymbol
  }

  var body: some View {
    HStack(spacing: 12) {
      // 收入卡片
      SummaryItemCard(
        title: "收入",
        amount: income,
        currencySymbol: currencySymbol,
        iconName: "arrow-down-right",
        iconColor: .cAccentGreen
      )

      // 支出卡片
      SummaryItemCard(
        title: "支出",
        amount: expense,
        currencySymbol: currencySymbol,
        iconName: "arrow-up-right",
        iconColor: .cAccentRed
      )
    }
  }
}

/// 单个统计项卡片
private struct SummaryItemCard: View {
  let title: String
  let amount: Double
  let currencySymbol: String
  let iconName: String
  let iconColor: Color

  var body: some View {
    VStack(alignment: .leading, spacing: 8) {
      HStack {
        Image(iconName)
          .foregroundStyle(iconColor)
          .font(.system(size: 16, weight: .semibold))
          .cornerRadius(8)
        Spacer()
      }
      VStack(alignment: .leading, spacing: 4) {
        Text(title)
          .font(.system(size: 12, weight: .medium))
          .foregroundColor(.cBlack.opacity(0.6))

        DisplayCurrencyView.size18(
          symbol: currencySymbol,
          amount: amount
        )
      }
    }
    .padding(12)
    .background(.cWhite.opacity(0.5))
    .cornerRadius(24)
    .overlay(
      RoundedRectangle(cornerRadius: 24)
        .strokeBorder(.cAccentBlue.opacity(0.08), lineWidth: 1)
    )
  }
}

// MARK: - Preview

#Preview {
  VStack(spacing: 16) {
    IncomeExpenseCard(
      income: 2500.00,
      expense: 1200.50,
      currencySymbol: "¥"
    )

    IncomeExpenseCard(
      income: 0.00,
      expense: 890.00,
      currencySymbol: "$"
    )

    IncomeExpenseCard(
      income: 1500.25,
      expense: 0.00,
      currencySymbol: "€"
    )
  }
  .padding()
  .background(.cLightBlue)
}
