//
//  CategoryActionSheet.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//

import SwiftUI

/// 交易分类操作弹窗
///
/// 提供交易分类相关的操作选项，包括编辑分类信息、排序类别、迁移账单到其他分类、删除分类等功能。
/// 使用统一的操作选项卡片样式，支持危险操作的视觉区分。
struct CategoryActionSheet: View {

  // MARK: - 属性

  /// 编辑分类回调
  let onEdit: () -> Void
  /// 排序类别回调
  let onSort: () -> Void
  /// 迁移账单回调
  let onMigrate: () -> Void
  /// 删除分类回调
  let onDelete: () -> Void
  /// 关闭弹窗回调
  let dismiss: () -> Void

  // MARK: - Environment

  /// 数据管理器
  @Environment(\.dataManager) private var dataManager

  // MARK: - 初始化

  /// 初始化交易分类操作弹窗
  /// - Parameters:
  ///   - onEdit: 编辑分类回调
  ///   - onSort: 排序类别回调
  ///   - onMigrate: 迁移账单回调
  ///   - onDelete: 删除分类回调
  ///   - dismiss: 关闭弹窗回调
  init(
    onEdit: @escaping () -> Void,
    onSort: @escaping () -> Void,
    onMigrate: @escaping () -> Void,
    onDelete: @escaping () -> Void,
    dismiss: @escaping () -> Void
  ) {
    self.onEdit = onEdit
    self.onSort = onSort
    self.onMigrate = onMigrate
    self.onDelete = onDelete
    self.dismiss = dismiss
  }

  // MARK: - 主体

  var body: some View {
    VStack(spacing: 12) {
      // 标题栏
      SheetTitle(
        title: "类别操作",
        button: "circle-x",
        rightButtonAction: {
          dataManager.hapticManager.trigger(.impactLight)
          dismiss()
        }
      )

      // MARK: 操作选项列表

      // 编辑类别
      SheetRow(
        title: "编辑类别",
        icon: "highlight"
      ) {
        dataManager.hapticManager.trigger(.impactLight)
        onEdit()
        dismiss()
      }

      // 类别排序
      SheetRow(
        title: "类别排序",
        icon: "arrow-top-bottom"
      ) {
        dataManager.hapticManager.trigger(.impactLight)
        onSort()
        dismiss()
      }

      // 迁移账单
      SheetRow(
        title: "迁移账单",
        icon: "arrow-rotate-right-left"
      ) {
        dataManager.hapticManager.trigger(.impactMedium)
        onMigrate()
        dismiss()
      }

      // 删除类别
      SheetRow(
        title: "删除类别",
        icon: "trash-can-simple",
        isDestructive: true
      ) {
        dataManager.hapticManager.trigger(.warning)
        onDelete()
        dismiss()
      }

      Spacer()
    }
  }
}

// MARK: - 预览代码 (Preview Provider)

#if DEBUG
  struct CategoryActionSheet_Previews: PreviewProvider {
    static var previews: some View {
      CategoryActionPreviewContainer()
    }
  }

  struct CategoryActionPreviewContainer: View {
    @State private var showSheet = false

    var body: some View {
      VStack {
        Button("显示类别操作菜单") {
          showSheet = true
        }
        .padding()
        .background(.cAccentBlue)
        .foregroundColor(.white)
        .cornerRadius(12)
      }
      .frame(maxWidth: .infinity, maxHeight: .infinity)
      .background(.cLightBlue)
      .floatingSheet(
        isPresented: $showSheet,
        config: SheetBase(
          maxDetent: .height(300),
          cornerRadius: 24,
          interactiveDimiss: true,
          hPadding: 8,
          bPadding: 4
        )
      ) {
        CategoryActionSheet(
          onEdit: { print("编辑类别") },
          onSort: { print("排序类别") },
          onMigrate: { print("迁移账单") },
          onDelete: { print("删除类别") },
          dismiss: { showSheet = false }
        )
        .environment(
          \.dataManager,
          DataManagement(
            cards: [],
            mainCategories: [],
            subCategories: [],
            currencies: [],
            recentTransactions: [],
            allTransactions: [],
            chatMessages: []
          ))
      }
    }
  }
#endif
