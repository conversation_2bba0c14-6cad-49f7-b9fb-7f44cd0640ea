<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg
PUBLIC "-//W3C//DTD SVG 1.1//EN"
     "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
     <!--Created with SF Symbol Generator (v1.0.0)-->

<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="3300" height="2200">
<!--glyph: "bank.medium", point size: 100.0-->
<style>.SFSymbolsPreviewWireframe {fill:none;opacity:1.0;stroke:black;stroke-width:0.5}
</style>
<g id="Notes">
<rect height="2200" id="artboard" style="fill:white;opacity:1" width="3300" x="0" y="0"/>
<line style="fill:none;stroke:black;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="292" y2="292"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 263 322)">Weight/Scale Variations</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 559.711 322)">Ultralight</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 856.422 322)">Thin</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1153.13 322)">Light</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1449.84 322)">Regular</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1746.56 322)">Medium</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2043.27 322)">Semibold</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2339.98 322)">Bold</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2636.69 322)">Heavy</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2933.4 322)">Black</text>
<line style="fill:none;stroke:black;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1903" y2="1903"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 263 1953)">Design Variations</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1971)">Symbols are supported in up to nine weights and three scales.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1989)">For optimal layout with text and other symbols, vertically align</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 2007)">symbols with the adjacent text.</text>
<line style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="776" x2="776" y1="1919" y2="1933"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 776 1953)">Margins</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 1971)">Leading and trailing margins on the left and right side of each symbol</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 1989)">can be adjusted by modifying the x-location of the margin guidelines.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 2007)">Modifications are automatically applied proportionally to all</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 2025)">scales and weights.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 1289 1953)">Exporting</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 1289 1971)">Symbols should be outlined when exporting to ensure the</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 1289 1989)">design is preserved when submitting to Xcode.</text>
<text id="template-version" style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1933)">Template v.5.0</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1951)">Requires Xcode 15 or greater</text>
<text id="descriptive-name" style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1969)">Generated from bank</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1987)">Typeset at 100.0 points</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 726)">Small</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1156)">Medium</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1586)">Large</text>
</g>
<g id="Guides">
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 696)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-S" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="696" y2="696"/>
<line id="Capline-S" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="625.541" y2="625.541"/>
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 1126)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-M" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1126" y2="1126"/>
<line id="Capline-M" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1055.54" y2="1055.54"/>
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 1556)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-L" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1556" y2="1556"/>
<line id="Capline-L" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1485.54" y2="1485.54"/>
<line id="left-margin-Ultralight-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="513.711" x2="513.711" y1="600.785" y2="720.121"/>
<line id="right-margin-Ultralight-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="605.711" x2="605.711" y1="600.785" y2="720.121"/>
<line id="left-margin-Regular-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="1403.84" x2="1403.84" y1="600.785" y2="720.121"/>
<line id="right-margin-Regular-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="1495.84" x2="1495.84" y1="600.785" y2="720.121"/>
<line id="left-margin-Black-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="2887.4" x2="2887.4" y1="600.785" y2="720.121"/>
<line id="right-margin-Black-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="2979.4" x2="2979.4" y1="600.785" y2="720.121"/>
</g>
<g id="Symbols">
<g id="Ultralight-S" transform="matrix(1 0 0 1 513.711 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="bank">
<path id="Intersect" fill-rule="evenodd" clip-rule="evenodd" d="M39.5163 9.6394C43.5925 7.58028 48.4075 7.58028 52.4837 9.6394L80.047 23.5652C82.6749 24.8927 84.3326 27.5846 84.3333 30.5281C84.3329 34.7918 80.9131 38.2523 76.6667 38.3258V64.942C79.443 66.5039 81.6305 69.0555 82.6862 72.2231C84.0425 76.2954 81.0122 80.4999 76.7191 80.4999H15.2809C10.9882 80.4999 7.95746 76.2956 9.3138 72.2231C10.3696 69.0557 12.5571 66.5039 15.3333 64.942V38.3258C11.087 38.2523 7.66712 34.7918 7.66667 30.5281C7.66735 27.5845 9.32571 24.8926 11.953 23.5652L39.5163 9.6394ZM21.7609 68.9999C18.5892 68.9999 15.7716 71.0301 14.7681 74.0386C14.6521 74.3883 14.9127 74.7499 15.2809 74.7499H76.7191C77.0872 74.7499 77.348 74.3885 77.2319 74.0386C76.2287 71.0302 73.4111 68.9999 70.2391 68.9999H21.7609ZM21.0833 63.2761C21.3076 63.2646 21.5344 63.2499 21.7609 63.2499H30.6667V38.3332H21.0833V63.2761ZM61.3333 63.2499H70.2391C70.4656 63.2499 70.6924 63.2646 70.9167 63.2761V38.3332H61.3333V63.2499ZM36.4167 63.2499H55.5833V38.3332H36.4167V63.2499ZM49.8895 14.7717C47.4436 13.5362 44.5564 13.5362 42.1105 14.7717L14.5435 28.6975C13.8532 29.047 13.4173 29.7544 13.4167 30.5281C13.4171 31.6622 14.3376 32.5832 15.4718 32.5832H76.5282C77.6625 32.5832 78.5829 31.6622 78.5833 30.5281C78.5826 29.7543 78.1469 29.047 77.4565 28.6975L49.8895 14.7717Z" fill="black"/>
</g>
</svg>

</g>
<g id="Regular-S" transform="matrix(1 0 0 1 1403.84 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="bank">
<path id="Intersect" fill-rule="evenodd" clip-rule="evenodd" d="M39.5163 9.6394C43.5925 7.58028 48.4075 7.58028 52.4837 9.6394L80.047 23.5652C82.6749 24.8927 84.3326 27.5846 84.3333 30.5281C84.3329 34.7918 80.9131 38.2523 76.6667 38.3258V64.942C79.443 66.5039 81.6305 69.0555 82.6862 72.2231C84.0425 76.2954 81.0122 80.4999 76.7191 80.4999H15.2809C10.9882 80.4999 7.95746 76.2956 9.3138 72.2231C10.3696 69.0557 12.5571 66.5039 15.3333 64.942V38.3258C11.087 38.2523 7.66712 34.7918 7.66667 30.5281C7.66735 27.5845 9.32571 24.8926 11.953 23.5652L39.5163 9.6394ZM21.7609 68.9999C18.5892 68.9999 15.7716 71.0301 14.7681 74.0386C14.6521 74.3883 14.9127 74.7499 15.2809 74.7499H76.7191C77.0872 74.7499 77.348 74.3885 77.2319 74.0386C76.2287 71.0302 73.4111 68.9999 70.2391 68.9999H21.7609ZM21.0833 63.2761C21.3076 63.2646 21.5344 63.2499 21.7609 63.2499H30.6667V38.3332H21.0833V63.2761ZM61.3333 63.2499H70.2391C70.4656 63.2499 70.6924 63.2646 70.9167 63.2761V38.3332H61.3333V63.2499ZM36.4167 63.2499H55.5833V38.3332H36.4167V63.2499ZM49.8895 14.7717C47.4436 13.5362 44.5564 13.5362 42.1105 14.7717L14.5435 28.6975C13.8532 29.047 13.4173 29.7544 13.4167 30.5281C13.4171 31.6622 14.3376 32.5832 15.4718 32.5832H76.5282C77.6625 32.5832 78.5829 31.6622 78.5833 30.5281C78.5826 29.7543 78.1469 29.047 77.4565 28.6975L49.8895 14.7717Z" fill="black"/>
</g>
</svg>

</g>
<g id="Black-S" transform="matrix(1 0 0 1 2887.4 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="bank">
<path id="Intersect" fill-rule="evenodd" clip-rule="evenodd" d="M39.5163 9.6394C43.5925 7.58028 48.4075 7.58028 52.4837 9.6394L80.047 23.5652C82.6749 24.8927 84.3326 27.5846 84.3333 30.5281C84.3329 34.7918 80.9131 38.2523 76.6667 38.3258V64.942C79.443 66.5039 81.6305 69.0555 82.6862 72.2231C84.0425 76.2954 81.0122 80.4999 76.7191 80.4999H15.2809C10.9882 80.4999 7.95746 76.2956 9.3138 72.2231C10.3696 69.0557 12.5571 66.5039 15.3333 64.942V38.3258C11.087 38.2523 7.66712 34.7918 7.66667 30.5281C7.66735 27.5845 9.32571 24.8926 11.953 23.5652L39.5163 9.6394ZM21.7609 68.9999C18.5892 68.9999 15.7716 71.0301 14.7681 74.0386C14.6521 74.3883 14.9127 74.7499 15.2809 74.7499H76.7191C77.0872 74.7499 77.348 74.3885 77.2319 74.0386C76.2287 71.0302 73.4111 68.9999 70.2391 68.9999H21.7609ZM21.0833 63.2761C21.3076 63.2646 21.5344 63.2499 21.7609 63.2499H30.6667V38.3332H21.0833V63.2761ZM61.3333 63.2499H70.2391C70.4656 63.2499 70.6924 63.2646 70.9167 63.2761V38.3332H61.3333V63.2499ZM36.4167 63.2499H55.5833V38.3332H36.4167V63.2499ZM49.8895 14.7717C47.4436 13.5362 44.5564 13.5362 42.1105 14.7717L14.5435 28.6975C13.8532 29.047 13.4173 29.7544 13.4167 30.5281C13.4171 31.6622 14.3376 32.5832 15.4718 32.5832H76.5282C77.6625 32.5832 78.5829 31.6622 78.5833 30.5281C78.5826 29.7543 78.1469 29.047 77.4565 28.6975L49.8895 14.7717Z" fill="black"/>
</g>
</svg>

</g>
</g>
</svg>