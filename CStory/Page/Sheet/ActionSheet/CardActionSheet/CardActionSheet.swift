//
//  CardActionSheet.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//

import SwiftUI

/// 卡片操作弹窗
///
/// 提供卡片相关的操作选项，包括编辑、删除等功能。
/// 支持两种删除模式：删除卡片及其所有交易记录，或仅删除卡片本身。
struct CardActionSheet: View {

  // MARK: - 属性

  /// 编辑卡片回调
  let onEditCard: () -> Void
  /// 删除卡片和交易记录回调
  let onDeleteWithTransactions: () -> Void
  /// 仅删除卡片回调
  let onDeleteCardOnly: () -> Void
  /// 关闭弹窗回调
  let dismiss: () -> Void

  // MARK: - Environment

  /// 数据管理器
  @Environment(\.dataManager) private var dataManager

  // MARK: - 初始化

  /// 初始化卡片操作弹窗
  /// - Parameters:
  ///   - onEditCard: 编辑卡片回调
  ///   - onDeleteWithTransactions: 删除卡片和交易记录回调
  ///   - onDeleteCardOnly: 仅删除卡片回调
  ///   - dismiss: 关闭弹窗回调
  init(
    onEditCard: @escaping () -> Void,
    onDeleteWithTransactions: @escaping () -> Void,
    onDeleteCardOnly: @escaping () -> Void,
    dismiss: @escaping () -> Void
  ) {
    self.onEditCard = onEditCard
    self.onDeleteWithTransactions = onDeleteWithTransactions
    self.onDeleteCardOnly = onDeleteCardOnly
    self.dismiss = dismiss
  }

  // MARK: - 主体

  var body: some View {
    VStack(spacing: 12) {
      // 标题栏
      SheetTitle(
        title: "卡片操作",
        button: "circle-x",
        rightButtonAction: {
          dataManager.hapticManager.trigger(.impactLight)
          dismiss()
        }
      )

      // MARK: 操作选项列表

      // 编辑卡片
      SheetRow(
        title: "编辑卡片",
        icon: "highlight"
      ) {
        dataManager.hapticManager.trigger(.impactLight)
        onEditCard()
        dismiss()
      }

      // 删除卡片和交易记录
      SheetRow(
        title: "删除卡片和交易记录",
        icon: "trash-can-simple",
        isDestructive: true
      ) {
        dataManager.hapticManager.trigger(.warning)
        onDeleteWithTransactions()
        dismiss()
      }

      // 仅删除卡片
      SheetRow(
        title: "仅删除卡片",
        icon: "trash-can-simple",
        isDestructive: true
      ) {
        dataManager.hapticManager.trigger(.warning)
        onDeleteCardOnly()
        dismiss()
      }

      Spacer()
    }
  }
}

// MARK: - 预览代码 (Preview Provider)

#if DEBUG
  struct CardActionSheet_Previews: PreviewProvider {
    static var previews: some View {
      CardActionPreviewContainer()
    }
  }

  struct CardActionPreviewContainer: View {
    @State private var showSheet = false

    var body: some View {
      VStack {
        Button("显示卡片操作菜单") {
          showSheet = true
        }
        .padding()
        .background(.cAccentBlue)
        .foregroundColor(.white)
        .cornerRadius(12)
      }
      .frame(maxWidth: .infinity, maxHeight: .infinity)
      .background(.cLightBlue)
      .floatingSheet(
        isPresented: $showSheet,
        config: SheetBase(
          maxDetent: .height(250),
          cornerRadius: 24,
          interactiveDimiss: true,
          hPadding: 8,
          bPadding: 4
        )
      ) {
        CardActionSheet(
          onEditCard: { print("编辑卡片") },
          onDeleteWithTransactions: { print("删除卡片和交易") },
          onDeleteCardOnly: { print("仅删除卡片") },
          dismiss: { showSheet = false }
        )
        .environment(
          \.dataManager,
          DataManagement(
            cards: [],
            mainCategories: [],
            subCategories: [],
            currencies: [],
            recentTransactions: [],
            allTransactions: [],
            chatMessages: []
          ))
      }
    }
  }
#endif
