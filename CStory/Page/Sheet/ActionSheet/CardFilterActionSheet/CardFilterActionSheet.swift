//
//  CardFilterActionSheet.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//

import SwiftUI

/// 卡片筛选操作弹窗
///
/// 提供卡片类型筛选功能，支持显示所有卡片、仅储蓄卡或仅信用卡。
/// 使用统一的选项卡片样式，带有选中状态指示。
struct CardFilterActionSheet: View {

  // MARK: - 筛选选项定义

  /// 筛选选项结构
  struct FilterOption {
    let title: String
    let filter: Bool?
    let icon: String
  }

  // MARK: - 属性

  /// 当前选中的筛选条件
  @Binding var selectedFilter: Bool?
  /// 关闭弹窗回调
  let dismiss: () -> Void
  /// 筛选条件改变回调
  let onFilterChanged: ((Bool?) -> Void)?

  // MARK: - Environment

  /// 数据管理器
  @Environment(\.dataManager) private var dataManager

  // MARK: - 初始化

  /// 初始化卡片筛选弹窗
  /// - Parameters:
  ///   - selectedFilter: 当前选中的筛选条件绑定
  ///   - dismiss: 关闭弹窗回调
  ///   - onFilterChanged: 筛选条件改变回调
  init(
    selectedFilter: Binding<Bool?>,
    dismiss: @escaping () -> Void,
    onFilterChanged: ((Bool?) -> Void)? = nil
  ) {
    self._selectedFilter = selectedFilter
    self.dismiss = dismiss
    self.onFilterChanged = onFilterChanged
  }

  // MARK: - 计算属性

  /// 筛选选项配置
  private let filterOptions: [FilterOption] = [
    FilterOption(title: "所有卡片", filter: nil, icon: "giro-cards"),
    FilterOption(title: "储蓄卡", filter: false, icon: "credit-card-1"),
    FilterOption(title: "信用卡", filter: true, icon: "credit-card-2"),
  ]

  /// 判断选项是否被选中
  private func isOptionSelected(_ option: FilterOption) -> Bool {
    return selectedFilter == option.filter
  }

  /// 处理选项点击
  private func handleOptionTap(_ option: FilterOption) {
    dataManager.hapticManager.trigger(.selection)

    if let onFilterChanged = onFilterChanged {
      // 如果提供了回调，使用回调处理
      onFilterChanged(option.filter)
    } else {
      // 否则直接更新绑定值并关闭
      selectedFilter = option.filter
      dismiss()
    }
  }

  // MARK: - 主体

  var body: some View {
    VStack(spacing: 12) {
      // 标题栏
      SheetTitle(
        title: "筛选卡片",
        button: "circle-x",
        rightButtonAction: {
          dataManager.hapticManager.trigger(.impactLight)
          dismiss()
        }
      )

      // MARK: 筛选选项列表
      ForEach(filterOptions, id: \.title) { option in
        SheetRow(
          title: option.title,
          icon: option.icon,
          isSelected: isOptionSelected(option)
        ) {
          handleOptionTap(option)
        }
      }

      Spacer()
    }
  }
}

// MARK: - 预览代码 (Preview Provider)

#if DEBUG
  struct CardFilterActionSheet_Previews: PreviewProvider {
    static var previews: some View {
      CardFilterActionPreviewContainer()
    }
  }

  struct CardFilterActionPreviewContainer: View {
    @State private var showSheet = false
    @State private var selectedFilter: Bool? = nil

    var body: some View {
      VStack {
        Button("显示卡片筛选菜单") {
          showSheet = true
        }
        .padding()
        .background(.cAccentBlue)
        .foregroundColor(.white)
        .cornerRadius(12)

        if let filter = selectedFilter {
          Text("当前筛选: \(filter ? "信用卡" : "储蓄卡")")
            .padding()
        } else {
          Text("当前筛选: 所有卡片")
            .padding()
        }
      }
      .frame(maxWidth: .infinity, maxHeight: .infinity)
      .background(.cLightBlue)
      .floatingSheet(
        isPresented: $showSheet,
        config: SheetBase(
          maxDetent: .height(200),
          cornerRadius: 24,
          interactiveDimiss: true,
          hPadding: 8,
          bPadding: 4
        )
      ) {
        CardFilterActionSheet(
          selectedFilter: $selectedFilter,
          dismiss: { showSheet = false },
          onFilterChanged: { filter in
            selectedFilter = filter
            print("筛选条件改变: \(filter?.description ?? "所有卡片")")
          }
        )
        .environment(
          \.dataManager,
          DataManagement(
            cards: [],
            mainCategories: [],
            subCategories: [],
            currencies: [],
            recentTransactions: [],
            allTransactions: [],
            chatMessages: []
          ))
      }
    }
  }
#endif
