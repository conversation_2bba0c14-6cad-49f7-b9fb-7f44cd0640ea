<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg
PUBLIC "-//W3C//DTD SVG 1.1//EN"
     "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
     <!--Created with SF Symbol Generator (v1.0.0)-->

<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="3300" height="2200">
<!--glyph: "arrow-up-right.medium", point size: 100.0-->
<style>.SFSymbolsPreviewWireframe {fill:none;opacity:1.0;stroke:black;stroke-width:0.5}
</style>
<g id="Notes">
<rect height="2200" id="artboard" style="fill:white;opacity:1" width="3300" x="0" y="0"/>
<line style="fill:none;stroke:black;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="292" y2="292"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 263 322)">Weight/Scale Variations</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 559.711 322)">Ultralight</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 856.422 322)">Thin</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1153.13 322)">Light</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1449.84 322)">Regular</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1746.56 322)">Medium</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2043.27 322)">Semibold</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2339.98 322)">Bold</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2636.69 322)">Heavy</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2933.4 322)">Black</text>
<line style="fill:none;stroke:black;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1903" y2="1903"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 263 1953)">Design Variations</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1971)">Symbols are supported in up to nine weights and three scales.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1989)">For optimal layout with text and other symbols, vertically align</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 2007)">symbols with the adjacent text.</text>
<line style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="776" x2="776" y1="1919" y2="1933"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 776 1953)">Margins</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 1971)">Leading and trailing margins on the left and right side of each symbol</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 1989)">can be adjusted by modifying the x-location of the margin guidelines.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 2007)">Modifications are automatically applied proportionally to all</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 2025)">scales and weights.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 1289 1953)">Exporting</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 1289 1971)">Symbols should be outlined when exporting to ensure the</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 1289 1989)">design is preserved when submitting to Xcode.</text>
<text id="template-version" style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1933)">Template v.5.0</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1951)">Requires Xcode 15 or greater</text>
<text id="descriptive-name" style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1969)">Generated from arrow-up-right</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1987)">Typeset at 100.0 points</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 726)">Small</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1156)">Medium</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1586)">Large</text>
</g>
<g id="Guides">
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 696)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-S" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="696" y2="696"/>
<line id="Capline-S" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="625.541" y2="625.541"/>
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 1126)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-M" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1126" y2="1126"/>
<line id="Capline-M" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1055.54" y2="1055.54"/>
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 1556)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-L" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1556" y2="1556"/>
<line id="Capline-L" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1485.54" y2="1485.54"/>
<line id="left-margin-Ultralight-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="513.711" x2="513.711" y1="600.785" y2="720.121"/>
<line id="right-margin-Ultralight-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="605.711" x2="605.711" y1="600.785" y2="720.121"/>
<line id="left-margin-Regular-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="1403.84" x2="1403.84" y1="600.785" y2="720.121"/>
<line id="right-margin-Regular-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="1495.84" x2="1495.84" y1="600.785" y2="720.121"/>
<line id="left-margin-Black-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="2887.4" x2="2887.4" y1="600.785" y2="720.121"/>
<line id="right-margin-Black-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="2979.4" x2="2979.4" y1="600.785" y2="720.121"/>
</g>
<g id="Symbols">
<g id="Ultralight-S" transform="matrix(1 0 0 1 513.711 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="arrow-up-right">
<path id="Subtract" fill-rule="evenodd" clip-rule="evenodd" d="M66.125 11.5C74.0643 11.5 80.5 17.936 80.5 25.875V66.125C80.5 74.0643 74.0643 80.5 66.125 80.5H25.875C17.936 80.5 11.5 74.0643 11.5 66.125V25.875C11.5 17.9359 17.9359 11.5 25.875 11.5H66.125ZM38.8125 28.2708C37.2248 28.2708 35.9376 29.5581 35.9375 31.1458C35.9375 32.7337 37.2247 34.0208 38.8125 34.0208H54.8721L29.1131 59.7798C27.9904 60.9025 27.9904 62.7225 29.1131 63.8452C30.2359 64.968 32.0558 64.968 33.1785 63.8452L58.9375 38.0863V54.1458C58.9375 55.7337 60.2247 57.0208 61.8125 57.0208C63.4002 57.0207 64.6875 55.7336 64.6875 54.1458V31.1458C64.6875 30.9744 64.6676 30.8074 64.6388 30.6442C64.603 30.4412 64.5463 30.2419 64.4666 30.049C64.3868 29.856 64.2852 29.6753 64.1672 29.5062C64.0715 29.3692 63.9674 29.2353 63.8452 29.1131C63.723 28.9909 63.5891 28.8868 63.4521 28.7912C63.283 28.6732 63.1024 28.5716 62.9093 28.4917C62.7864 28.441 62.6588 28.4086 62.5312 28.3757C62.4809 28.3626 62.4328 28.3411 62.3815 28.3307C62.3592 28.3262 62.3366 28.3235 62.3141 28.3195C62.2448 28.3073 62.1754 28.293 62.1045 28.2858L61.8125 28.2708H38.8125Z" fill="black"/>
</g>
</svg>

</g>
<g id="Regular-S" transform="matrix(1 0 0 1 1403.84 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="arrow-up-right">
<path id="Subtract" fill-rule="evenodd" clip-rule="evenodd" d="M66.125 11.5C74.0643 11.5 80.5 17.936 80.5 25.875V66.125C80.5 74.0643 74.0643 80.5 66.125 80.5H25.875C17.936 80.5 11.5 74.0643 11.5 66.125V25.875C11.5 17.9359 17.9359 11.5 25.875 11.5H66.125ZM38.8125 28.2708C37.2248 28.2708 35.9376 29.5581 35.9375 31.1458C35.9375 32.7337 37.2247 34.0208 38.8125 34.0208H54.8721L29.1131 59.7798C27.9904 60.9025 27.9904 62.7225 29.1131 63.8452C30.2359 64.968 32.0558 64.968 33.1785 63.8452L58.9375 38.0863V54.1458C58.9375 55.7337 60.2247 57.0208 61.8125 57.0208C63.4002 57.0207 64.6875 55.7336 64.6875 54.1458V31.1458C64.6875 30.9744 64.6676 30.8074 64.6388 30.6442C64.603 30.4412 64.5463 30.2419 64.4666 30.049C64.3868 29.856 64.2852 29.6753 64.1672 29.5062C64.0715 29.3692 63.9674 29.2353 63.8452 29.1131C63.723 28.9909 63.5891 28.8868 63.4521 28.7912C63.283 28.6732 63.1024 28.5716 62.9093 28.4917C62.7864 28.441 62.6588 28.4086 62.5312 28.3757C62.4809 28.3626 62.4328 28.3411 62.3815 28.3307C62.3592 28.3262 62.3366 28.3235 62.3141 28.3195C62.2448 28.3073 62.1754 28.293 62.1045 28.2858L61.8125 28.2708H38.8125Z" fill="black"/>
</g>
</svg>

</g>
<g id="Black-S" transform="matrix(1 0 0 1 2887.4 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="arrow-up-right">
<path id="Subtract" fill-rule="evenodd" clip-rule="evenodd" d="M66.125 11.5C74.0643 11.5 80.5 17.936 80.5 25.875V66.125C80.5 74.0643 74.0643 80.5 66.125 80.5H25.875C17.936 80.5 11.5 74.0643 11.5 66.125V25.875C11.5 17.9359 17.9359 11.5 25.875 11.5H66.125ZM38.8125 28.2708C37.2248 28.2708 35.9376 29.5581 35.9375 31.1458C35.9375 32.7337 37.2247 34.0208 38.8125 34.0208H54.8721L29.1131 59.7798C27.9904 60.9025 27.9904 62.7225 29.1131 63.8452C30.2359 64.968 32.0558 64.968 33.1785 63.8452L58.9375 38.0863V54.1458C58.9375 55.7337 60.2247 57.0208 61.8125 57.0208C63.4002 57.0207 64.6875 55.7336 64.6875 54.1458V31.1458C64.6875 30.9744 64.6676 30.8074 64.6388 30.6442C64.603 30.4412 64.5463 30.2419 64.4666 30.049C64.3868 29.856 64.2852 29.6753 64.1672 29.5062C64.0715 29.3692 63.9674 29.2353 63.8452 29.1131C63.723 28.9909 63.5891 28.8868 63.4521 28.7912C63.283 28.6732 63.1024 28.5716 62.9093 28.4917C62.7864 28.441 62.6588 28.4086 62.5312 28.3757C62.4809 28.3626 62.4328 28.3411 62.3815 28.3307C62.3592 28.3262 62.3366 28.3235 62.3141 28.3195C62.2448 28.3073 62.1754 28.293 62.1045 28.2858L61.8125 28.2708H38.8125Z" fill="black"/>
</g>
</svg>

</g>
</g>
</svg>