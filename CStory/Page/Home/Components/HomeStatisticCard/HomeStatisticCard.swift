//
//  HomeStatisticCard.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//

import SwiftUI

/// 统计卡片组件
///
/// 用于展示各类统计数据的卡片，如总资产、总负债、月度收支等。
/// 支持标题、金额显示，可选图标，自动处理金额格式化。
///
/// ## 使用示例
/// ```swift
/// HomeStatisticCard(
///     title: "总资产",
///     amount: 15234.67,
///     currencySymbol: "¥",
///     iconName: "TotalCardsIcon"
/// )
/// ```
struct HomeStatisticCard: View {
  // MARK: - Properties

  let title: String
  let amount: Double
  let currencySymbol: String
  let iconName: String?

  // MARK: - 初始化

  init(
    title: String,
    amount: Double,
    currencySymbol: String = "¥",
    iconName: String? = nil
  ) {
    self.title = title
    self.amount = amount
    self.currencySymbol = currencySymbol
    self.iconName = iconName
  }

  // MARK: - 视图主体

  var body: some View {
    VStack(alignment: .leading, spacing: 2) {
      // 标题和可选图标
      HStack(spacing: 4) {
        // 可选图标
        if let iconName = iconName {
          Image(iconName)
            .resizable()
            .frame(width: 24, height: 24)
            .foregroundColor(.cBlack.opacity(0.6))
        }

        Text(title)
          .font(.system(size: 13, weight: .medium))
          .foregroundColor(.cBlack.opacity(0.6))
          .frame(maxWidth: .infinity, alignment: .leading)
      }

      // 金额显示 - 使用中等尺寸的货币显示
      DisplayCurrencyView.size18(
        symbol: currencySymbol,
        amount: amount
      )

    }
    .padding(.horizontal, 12)
    .padding(.vertical, 8)
    .frame(maxWidth: .infinity, maxHeight: .infinity)  // 填满父视图
    .background(.cWhite.opacity(0.5))
    .cornerRadius(24)
    .overlay(
      RoundedRectangle(cornerRadius: 24)
        .strokeBorder(Color.accentColor.opacity(0.08), lineWidth: 1)
    )
  }
}

// MARK: - 预览

#Preview {
  VStack(spacing: 12) {
    // 带图标的卡片
    HomeStatisticCard(
      title: "总资产",
      amount: 15234.67,
      currencySymbol: "¥",
      iconName: "TotalCardsIcon"
    )

    // 不带图标的卡片
    HomeStatisticCard(
      title: "总负债",
      amount: 3456.78,
      currencySymbol: "¥"
    )
  }
  .padding()
  .background(Color.gray.opacity(0.1))
}
