import SwiftUI

/// 记账设置页面
///
/// 用户可以在这里配置默认的记账方式、默认卡片等偏好设置
struct TransactionSettingsView: View {

  // MARK: - Properties
  @Environment(\.dismiss) private var dismiss
  @Environment(\.dataManager) private var dataManager
  @EnvironmentObject private var pathManager: PathManagerHelper
  @StateObject private var viewModel: TransactionSettingsVM

  // Sheet状态
  @State private var showExpenseCardSheet = false
  @State private var showIncomeCardSheet = false

  // MARK: - Initialization

  init() {
    self._viewModel = StateObject(wrappedValue: TransactionSettingsVM())
  }

  // MARK: - Body

  var body: some View {
    mainContent
      .floatingSheet(
        isPresented: $showExpenseCardSheet,
        config: SheetBase(
          maxDetent: .fraction(0.6),
          cornerRadius: 24,
          interactiveDimiss: true,
          hPadding: 8,
          bPadding: 4
        )
      ) {
        expenseCardSheet
      }
      .floatingSheet(
        isPresented: $showIncomeCardSheet,
        config: SheetBase(
          maxDetent: .fraction(0.6),
          cornerRadius: 24,
          interactiveDimiss: true,
          hPadding: 8,
          bPadding: 4
        )
      ) {
        incomeCardSheet
      }
  }

  // MARK: - Subviews

  /// 主要内容
  private var mainContent: some View {
    VStack(spacing: 0) {
      // MARK: 导航栏
      NavigationBarKit(
        title: "记账设置",
        onBack: {
          dataManager.hapticManager.trigger(.impactLight)
          dismiss()
        }
      )

      // MARK: 主要内容
      ScrollView {
        VStack(spacing: 12) {

          StatusCardRow(
            title: "默认记账方式",
            rightContent: .segmentedPicker(
              options: [.text("手动"), .text("AI")],
              selectedIndex: viewModel.selectedRecordingMode == .manual ? 0 : 1,
              onSelect: { index in
                dataManager.hapticManager.trigger(.selection)
                viewModel.setRecordingMode(index == 0 ? .manual : .ai)
              }
            )
          )

          // 只在AI记账模式下显示AI输入方式设置
          if viewModel.selectedRecordingMode == .ai {
            aiInputModeRow
          }

          // 只在手动记账模式下显示默认卡片设置
          if viewModel.selectedRecordingMode == .manual {
            // 默认支出卡片
              Row(
                leftMainText: "默认支出卡片",
                leftSubText: "自动选择第一张卡片",
                rightMainText: "右主文案",
                rightMainIcon: "chevron-right",
                onRowTap: {
                    dataManager.hapticManager.trigger(.selection)
                    showExpenseCardSheet = true
                }
              )
              // 默认收入卡片
                Row(
                  leftMainText: "默认收入卡片",
                  leftSubText: "自动选择第一张卡片",
                  rightMainText: "右主文案",
                  rightMainIcon: "chevron-right",
                  onRowTap: {
                      dataManager.hapticManager.trigger(.selection)
                      showIncomeCardSheet = true
                  }
                )
              
              // 默认支出卡片
            cardSelectionRow(
              title: "默认支出卡片",
              subtitle: "自动选择第一张储蓄卡",
              selectedCard: viewModel.defaultExpenseCard,
              icon: "minus.circle.fill",
              onTap: {
                dataManager.hapticManager.trigger(.selection)
                showExpenseCardSheet = true
              }
            )

            
              // 默认收入卡片
            cardSelectionRow(
              title: "默认收入卡片",
              subtitle: "自动选择第一张储蓄卡",
              selectedCard: viewModel.defaultIncomeCard,
              icon: "plus.circle.fill",
              onTap: {
                dataManager.hapticManager.trigger(.selection)
                showIncomeCardSheet = true
              }
            )
          }

        }
        .padding(.horizontal, 16)
        .padding(.top, 12)
        .padding(.bottom, 80)
      }
      .background(.cLightBlue)
    }
    .background(.cLightBlue)
    .navigationBarTitleDisplayMode(.inline)
    .navigationBarBackButtonHidden(true)
    .onAppear {
      viewModel.loadData(dataManager: dataManager)
    }
  }

  /// 设置区域

  /// 支出卡片选择Sheet
  private var expenseCardSheet: some View {
    cardSelectionSheet(
      title: "选择默认支出卡片",
      selectedCard: viewModel.defaultExpenseCard,
      onSelect: { card in
        viewModel.setDefaultExpenseCard(card)
        showExpenseCardSheet = false
      },
      onCancel: {
        showExpenseCardSheet = false
      }
    )
  }

  /// 收入卡片选择Sheet
  private var incomeCardSheet: some View {
    cardSelectionSheet(
      title: "选择默认收入卡片",
      selectedCard: viewModel.defaultIncomeCard,
      onSelect: { card in
        viewModel.setDefaultIncomeCard(card)
        showIncomeCardSheet = false
      },
      onCancel: {
        showIncomeCardSheet = false
      }
    )
  }

  /// AI输入方式行
  private var aiInputModeRow: some View {
    StatusCardRow(
      title: "AI输入方式",
      rightContent: .segmentedPicker(
        options: [.text("文字"), .text("语音")],
        selectedIndex: viewModel.selectedAIInputMode == .text ? 0 : 1,
        onSelect: { index in
          dataManager.hapticManager.trigger(.selection)
          viewModel.setAIInputMode(index == 0 ? .text : .voice)
        }
      )
    )
  }

  /// 卡片选择行
  private func cardSelectionRow(
    title: String,
    subtitle: String,
    selectedCard: CardModel?,
    icon: String,
    onTap: @escaping () -> Void
  ) -> some View {
    Button(action: onTap) {
      StatusCardRow(
        title: title,
        subtitle: subtitle,
        rightContent: .text(
          selectedCard?.name ?? "未设置",
          color: selectedCard != nil ? .cBlack.opacity(0.6) : .cBlack.opacity(0.4)
        )
      )
    }
    .buttonStyle(PlainButtonStyle())
  }

  /// 卡片选择Sheet
  private func cardSelectionSheet(
    title: String,
    selectedCard: CardModel?,
    onSelect: @escaping (CardModel?) -> Void,
    onCancel: @escaping () -> Void
  ) -> some View {
    VStack(spacing: 20) {
      // 使用SheetTitle组件
      SheetTitle(
        title: title,
        button: "circle-x",
        rightButtonAction: onCancel
      )

      // 不设置选项
      Button(action: {
        dataManager.hapticManager.trigger(.selection)
        onSelect(nil)
      }) {
        HStack {
          Image(selectedCard == nil ? "circle-check" : "circle-placeholder-on")
            .font(.system(size: 20))
            .foregroundColor(selectedCard == nil ? .accentColor : .gray)

          Text("不设置默认卡片")
            .font(.system(size: 15, weight: .medium))
            .foregroundColor(.cBlack)

          Spacer()
        }
        .padding(16)
        .background(
          RoundedRectangle(cornerRadius: 24)
            .fill(.cWhite.opacity(0.5))
        )
      }
      .buttonStyle(PlainButtonStyle())
      .padding(.horizontal, 16)

      // 卡片列表
      ScrollView {
        LazyVStack(spacing: 12) {
          ForEach(viewModel.availableCards, id: \.id) { card in
            let cardViewModel = CardRowVM(
              from: card,
              isSelected: selectedCard?.id == card.id,
              showTypeTag: true,
              showAdditionalInfo: false
            )
            CardRow(
              viewModel: cardViewModel,
              onTap: {
                dataManager.hapticManager.trigger(.selection)
                onSelect(card)
              }
            )
          }
        }
        .padding(.horizontal, 16)
      }

      Spacer()
    }
  }

}

#Preview {
  TransactionSettingsView()
    .withDataManager(DataManagement())
}
