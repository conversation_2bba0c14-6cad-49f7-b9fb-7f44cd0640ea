// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		7C33FCF32E559FEF0041274B /* Alamofire in Frameworks */ = {isa = PBXBuildFile; productRef = 7C33FCF22E559FEF0041274B /* Alamofire */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		7CDDB6962E559C9000CB70AF /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 7CDDB67C2E559C8F00CB70AF /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 7CDDB6832E559C8F00CB70AF;
			remoteInfo = CStory;
		};
		7CDDB6A02E559C9100CB70AF /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 7CDDB67C2E559C8F00CB70AF /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 7CDDB6832E559C8F00CB70AF;
			remoteInfo = CStory;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		7CDDB6842E559C8F00CB70AF /* CStory.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = CStory.app; sourceTree = BUILT_PRODUCTS_DIR; };
		7CDDB6952E559C9000CB70AF /* CStoryTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = CStoryTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		7CDDB69F2E559C9100CB70AF /* CStoryUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = CStoryUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		7CDDB6A72E559C9100CB70AF /* Exceptions for "CStory" folder in "CStory" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 7CDDB6832E559C8F00CB70AF /* CStory */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		7CDDB6862E559C8F00CB70AF /* CStory */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				7CDDB6A72E559C9100CB70AF /* Exceptions for "CStory" folder in "CStory" target */,
			);
			path = CStory;
			sourceTree = "<group>";
		};
		7CDDB6982E559C9000CB70AF /* CStoryTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = CStoryTests;
			sourceTree = "<group>";
		};
		7CDDB6A22E559C9100CB70AF /* CStoryUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = CStoryUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		7CDDB6812E559C8F00CB70AF /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				7C33FCF32E559FEF0041274B /* Alamofire in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7CDDB6922E559C9000CB70AF /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7CDDB69C2E559C9100CB70AF /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		7CDDB67B2E559C8F00CB70AF = {
			isa = PBXGroup;
			children = (
				7CDDB6862E559C8F00CB70AF /* CStory */,
				7CDDB6982E559C9000CB70AF /* CStoryTests */,
				7CDDB6A22E559C9100CB70AF /* CStoryUITests */,
				7CDDB6852E559C8F00CB70AF /* Products */,
			);
			sourceTree = "<group>";
		};
		7CDDB6852E559C8F00CB70AF /* Products */ = {
			isa = PBXGroup;
			children = (
				7CDDB6842E559C8F00CB70AF /* CStory.app */,
				7CDDB6952E559C9000CB70AF /* CStoryTests.xctest */,
				7CDDB69F2E559C9100CB70AF /* CStoryUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		7CDDB6832E559C8F00CB70AF /* CStory */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7CDDB6A82E559C9100CB70AF /* Build configuration list for PBXNativeTarget "CStory" */;
			buildPhases = (
				7CDDB6802E559C8F00CB70AF /* Sources */,
				7CDDB6812E559C8F00CB70AF /* Frameworks */,
				7CDDB6822E559C8F00CB70AF /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				7CDDB6862E559C8F00CB70AF /* CStory */,
			);
			name = CStory;
			packageProductDependencies = (
				7C33FCF22E559FEF0041274B /* Alamofire */,
			);
			productName = CStory;
			productReference = 7CDDB6842E559C8F00CB70AF /* CStory.app */;
			productType = "com.apple.product-type.application";
		};
		7CDDB6942E559C9000CB70AF /* CStoryTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7CDDB6AD2E559C9100CB70AF /* Build configuration list for PBXNativeTarget "CStoryTests" */;
			buildPhases = (
				7CDDB6912E559C9000CB70AF /* Sources */,
				7CDDB6922E559C9000CB70AF /* Frameworks */,
				7CDDB6932E559C9000CB70AF /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				7CDDB6972E559C9000CB70AF /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				7CDDB6982E559C9000CB70AF /* CStoryTests */,
			);
			name = CStoryTests;
			packageProductDependencies = (
			);
			productName = CStoryTests;
			productReference = 7CDDB6952E559C9000CB70AF /* CStoryTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		7CDDB69E2E559C9100CB70AF /* CStoryUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7CDDB6B02E559C9100CB70AF /* Build configuration list for PBXNativeTarget "CStoryUITests" */;
			buildPhases = (
				7CDDB69B2E559C9100CB70AF /* Sources */,
				7CDDB69C2E559C9100CB70AF /* Frameworks */,
				7CDDB69D2E559C9100CB70AF /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				7CDDB6A12E559C9100CB70AF /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				7CDDB6A22E559C9100CB70AF /* CStoryUITests */,
			);
			name = CStoryUITests;
			packageProductDependencies = (
			);
			productName = CStoryUITests;
			productReference = 7CDDB69F2E559C9100CB70AF /* CStoryUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		7CDDB67C2E559C8F00CB70AF /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					7CDDB6832E559C8F00CB70AF = {
						CreatedOnToolsVersion = 16.4;
					};
					7CDDB6942E559C9000CB70AF = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 7CDDB6832E559C8F00CB70AF;
					};
					7CDDB69E2E559C9100CB70AF = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 7CDDB6832E559C8F00CB70AF;
					};
				};
			};
			buildConfigurationList = 7CDDB67F2E559C8F00CB70AF /* Build configuration list for PBXProject "CStory" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 7CDDB67B2E559C8F00CB70AF;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				7CDDB9AE2E559DC200CB70AF /* XCRemoteSwiftPackageReference "Alamofire" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 7CDDB6852E559C8F00CB70AF /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				7CDDB6832E559C8F00CB70AF /* CStory */,
				7CDDB6942E559C9000CB70AF /* CStoryTests */,
				7CDDB69E2E559C9100CB70AF /* CStoryUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		7CDDB6822E559C8F00CB70AF /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7CDDB6932E559C9000CB70AF /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7CDDB69D2E559C9100CB70AF /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		7CDDB6802E559C8F00CB70AF /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7CDDB6912E559C9000CB70AF /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7CDDB69B2E559C9100CB70AF /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		7CDDB6972E559C9000CB70AF /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 7CDDB6832E559C8F00CB70AF /* CStory */;
			targetProxy = 7CDDB6962E559C9000CB70AF /* PBXContainerItemProxy */;
		};
		7CDDB6A12E559C9100CB70AF /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 7CDDB6832E559C8F00CB70AF /* CStory */;
			targetProxy = 7CDDB6A02E559C9100CB70AF /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		7CDDB6A92E559C9100CB70AF /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = CStory/CStory.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = UK45A84643;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = CStory/Info.plist;
				INFOPLIST_KEY_NSCameraUsageDescription = "用于拍摄账单、收据和交易凭证，帮助您快速记录和管理财务信息";
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "通过语音输入快速记录交易信息，提升记账效率";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "用于选择账单、收据和交易凭证图片，帮助您快速记录和管理财务信息";
				INFOPLIST_KEY_NSSpeechRecognitionUsageDescription = "通过语音输入快速记录交易信息，提升记账效率";
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = cc.nzue.cstory;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Debug;
		};
		7CDDB6AA2E559C9100CB70AF /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = CStory/CStory.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = UK45A84643;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = CStory/Info.plist;
				INFOPLIST_KEY_NSCameraUsageDescription = "用于拍摄账单、收据和交易凭证，帮助您快速记录和管理财务信息";
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "通过语音输入快速记录交易信息，提升记账效率";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "用于选择账单、收据和交易凭证图片，帮助您快速记录和管理财务信息";
				INFOPLIST_KEY_NSSpeechRecognitionUsageDescription = "通过语音输入快速记录交易信息，提升记账效率";
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = cc.nzue.cstory;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Release;
		};
		7CDDB6AB2E559C9100CB70AF /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = UK45A84643;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		7CDDB6AC2E559C9100CB70AF /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = UK45A84643;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		7CDDB6AE2E559C9100CB70AF /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = UK45A84643;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = cc.nzue.CStoryTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/CStory.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/CStory";
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Debug;
		};
		7CDDB6AF2E559C9100CB70AF /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = UK45A84643;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = cc.nzue.CStoryTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/CStory.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/CStory";
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Release;
		};
		7CDDB6B12E559C9100CB70AF /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = UK45A84643;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = cc.nzue.CStoryUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = CStory;
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Debug;
		};
		7CDDB6B22E559C9100CB70AF /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = UK45A84643;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = cc.nzue.CStoryUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = CStory;
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		7CDDB67F2E559C8F00CB70AF /* Build configuration list for PBXProject "CStory" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7CDDB6AB2E559C9100CB70AF /* Debug */,
				7CDDB6AC2E559C9100CB70AF /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7CDDB6A82E559C9100CB70AF /* Build configuration list for PBXNativeTarget "CStory" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7CDDB6A92E559C9100CB70AF /* Debug */,
				7CDDB6AA2E559C9100CB70AF /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7CDDB6AD2E559C9100CB70AF /* Build configuration list for PBXNativeTarget "CStoryTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7CDDB6AE2E559C9100CB70AF /* Debug */,
				7CDDB6AF2E559C9100CB70AF /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7CDDB6B02E559C9100CB70AF /* Build configuration list for PBXNativeTarget "CStoryUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7CDDB6B12E559C9100CB70AF /* Debug */,
				7CDDB6B22E559C9100CB70AF /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		7CDDB9AE2E559DC200CB70AF /* XCRemoteSwiftPackageReference "Alamofire" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/Alamofire/Alamofire";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.10.2;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		7C33FCF22E559FEF0041274B /* Alamofire */ = {
			isa = XCSwiftPackageProductDependency;
			package = 7CDDB9AE2E559DC200CB70AF /* XCRemoteSwiftPackageReference "Alamofire" */;
			productName = Alamofire;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 7CDDB67C2E559C8F00CB70AF /* Project object */;
}
