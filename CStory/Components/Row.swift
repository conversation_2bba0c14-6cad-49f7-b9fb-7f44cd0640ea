//
//  Row.swift
//  CStory
//
//  Created by nzu<PERSON> on 2025/8/20.
//

import SwiftUI

struct Row: View {
  // 可选参数，传值就显示，不传就不显示
  let leftIconName: String?
  let imageName: String?
  let leftMainText: String?
  let leftSubText: String?
  let tagText: String?
  let rightMainText: String?
  let rightSubText: String?
  let rightMainIcon: String?
  let rightSubIcon: String?
  let selectBorder: Bool?

  // 回调函数
  let onLeftIconTap: (() -> Void)?
  let onRightMainIconTap: (() -> Void)?
  let onRightSubIconTap: (() -> Void)?
  let onRowTap: (() -> Void)?

  // 初始化方法，所有参数都是可选的
  init(
    leftIconName: String? = nil,
    imageName: String? = nil,
    leftMainText: String? = nil,
    leftSubText: String? = nil,
    tagText: String? = nil,
    rightMainText: String? = nil,
    rightSubText: String? = nil,
    rightMainIcon: String? = nil,
    rightSubIcon: String? = nil,
    selectBorder: Bool? = nil,
    onLeftIconTap: (() -> Void)? = nil,
    onRightMainIconTap: (() -> Void)? = nil,
    onRightSubIconTap: (() -> Void)? = nil,
    onRowTap: (() -> Void)? = nil
  ) {
    self.leftIconName = leftIconName
    self.imageName = imageName
    self.leftMainText = leftMainText
    self.leftSubText = leftSubText
    self.tagText = tagText
    self.rightMainText = rightMainText
    self.rightSubText = rightSubText
    self.rightMainIcon = rightMainIcon
    self.rightSubIcon = rightSubIcon
    self.selectBorder = selectBorder
    self.onLeftIconTap = onLeftIconTap
    self.onRightMainIconTap = onRightMainIconTap
    self.onRightSubIconTap = onRightSubIconTap
    self.onRowTap = onRowTap
  }

  var body: some View {
    HStack(alignment: .center) {
      HStack(spacing: 4) {
        HStack(spacing: 12.0) {
          if let leftIconName = leftIconName {
            Image(leftIconName)
              .font(.system(size: 16, weight: .regular))
              .foregroundColor(.cBlack.opacity(0.4))
              .frame(width: 24, height: 24)
              .onTapGesture {
                onLeftIconTap?()
              }
          }
          // 只有传入 imageName 时才显示图片
          if let imageName = imageName {
            Image(imageName)
              .resizable()
              .frame(width: 44, height: 44)
              .cornerRadius(16)
          }
          VStack(alignment: .leading, spacing: 4.0) {
            HStack(alignment: .center) {
              // 只有传入 leftMainText 时才显示左主文案
              if let leftMainText = leftMainText {
                Text(leftMainText)
                  .font(.system(size: 14, weight: .medium))
                  .foregroundColor(.cBlack)
              }
              // 只有传入 tagText 时才显示标签文案
              if let tagText = tagText {
                Text(tagText)
                  .font(.system(size: 10, weight: .medium))
                  .foregroundColor(.cWhite)
                  .padding(.horizontal, 4)
                  .padding(.vertical, 2)
                  .background(.cAccentRed)
                  .cornerRadius(12)
              }
            }
            // 只有传入 leftSubText 时才显示左副文案
            if let leftSubText = leftSubText {
              Text(leftSubText)
                .font(.system(size: 12, weight: .regular))
                .foregroundColor(.cBlack.opacity(0.4))
            }
          }
          .padding(.leading, 4.0)
        }
      }
      Spacer()
      HStack(alignment: .center, spacing: 0.0) {
        VStack(alignment: .trailing, spacing: 4.0) {
          // 只有传入 rightMainText 时才显示右主文案
          if let rightMainText = rightMainText {
            Text(rightMainText)
              .font(.system(size: 14, weight: .medium))
              .foregroundColor(.cBlack)
          }
          // 只有传入 rightSubText 时才显示右副文案
          if let rightSubText = rightSubText {
            Text(rightSubText)
              .font(.system(size: 12, weight: .regular))
              .foregroundColor(.cBlack.opacity(0.4))
          }
        }
        .padding(.trailing, 8.0)
        HStack(spacing: 8.0) {
          if let rightMainIcon = rightMainIcon {

            Image(rightMainIcon)
              .font(.system(size: 16, weight: .regular))
              .foregroundColor(.cBlack.opacity(0.4))
              .frame(width: 24, height: 24)
              .onTapGesture {
                onRightMainIconTap?()
              }

          }
          if let rightSubIcon = rightSubIcon {

            Image(rightSubIcon)
              .font(.system(size: 16, weight: .regular))
              .foregroundColor(.cBlack.opacity(0.4))
              .frame(width: 24, height: 24)
              .onTapGesture {
                onRightSubIconTap?()
              }

          }
        }

      }

    }
    .padding(.vertical, 8)
    .padding(.horizontal, 12)
    .frame(minHeight: 56)
    .background(.cWhite.opacity(0.5))
    .cornerRadius(24)
    .overlay(
      RoundedRectangle(cornerRadius: 24)
        .strokeBorder(.cAccentBlue, lineWidth: selectBorder == true ? 1 : 0)
    )
    .onTapGesture {
      onRowTap?()
    }

  }
}

#Preview {
  VStack(spacing: 12.0) {
    Spacer()
    // 示例：传入所有参数和回调函数
    Row(
      leftIconName: "circle-placeholder-on",
      imageName: "爱西爱西爱银行",
      leftMainText: "左主文案",
      leftSubText: "左副文案",
      tagText: "标签文案",
      rightMainText: "右主文案",
      rightSubText: "右副文案",
      rightMainIcon: "highlight",
      rightSubIcon: "circle-placeholder-on",
      onLeftIconTap: {
        print("左侧图标被点击")
      },
      onRightMainIconTap: {
        print("右主图标被点击")
      },
      onRightSubIconTap: {
        print("右副图标被点击")
      },
      onRowTap: {
        print("整行被点击")
      }
    )

    //选择卡片行
    Row(
      imageName: "爱西爱西爱银行",
      leftMainText: "左主文案",
      leftSubText: "左副文案",
      rightMainText: "右主文案",
      selectBorder: true
    )
    //选择货币行
    Row(
      leftMainText: "左主文案",
      leftSubText: "左副文案",
      rightMainText: "右主文案",
    )
    //货币设置行
    Row(
      leftIconName: "circle-placeholder-on",
      leftMainText: "左主文案",
      leftSubText: "左副文案",
      rightMainText: "右主文案",
      rightSubIcon: "chevron-right",
      onRowTap: {
        print("整行被点击")
      }
    )
    // 操作选项行
    Row(
      leftIconName: "circle-placeholder-on",
      leftMainText: "左主文案",
      onRowTap: {
        print("整行被点击")
      }
    )

    Spacer()
  }
  .padding(.horizontal, 16)
  .background(.cBeige)

}
