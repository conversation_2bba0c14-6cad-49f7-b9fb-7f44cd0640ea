<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg
PUBLIC "-//W3C//DTD SVG 1.1//EN"
     "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
     <!--Created with SF Symbol Generator (v1.0.0)-->

<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="3300" height="2200">
<!--glyph: "star.medium", point size: 100.0-->
<style>.SFSymbolsPreviewWireframe {fill:none;opacity:1.0;stroke:black;stroke-width:0.5}
</style>
<g id="Notes">
<rect height="2200" id="artboard" style="fill:white;opacity:1" width="3300" x="0" y="0"/>
<line style="fill:none;stroke:black;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="292" y2="292"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 263 322)">Weight/Scale Variations</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 559.711 322)">Ultralight</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 856.422 322)">Thin</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1153.13 322)">Light</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1449.84 322)">Regular</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1746.56 322)">Medium</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2043.27 322)">Semibold</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2339.98 322)">Bold</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2636.69 322)">Heavy</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2933.4 322)">Black</text>
<line style="fill:none;stroke:black;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1903" y2="1903"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 263 1953)">Design Variations</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1971)">Symbols are supported in up to nine weights and three scales.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1989)">For optimal layout with text and other symbols, vertically align</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 2007)">symbols with the adjacent text.</text>
<line style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="776" x2="776" y1="1919" y2="1933"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 776 1953)">Margins</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 1971)">Leading and trailing margins on the left and right side of each symbol</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 1989)">can be adjusted by modifying the x-location of the margin guidelines.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 2007)">Modifications are automatically applied proportionally to all</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 2025)">scales and weights.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 1289 1953)">Exporting</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 1289 1971)">Symbols should be outlined when exporting to ensure the</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 1289 1989)">design is preserved when submitting to Xcode.</text>
<text id="template-version" style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1933)">Template v.5.0</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1951)">Requires Xcode 15 or greater</text>
<text id="descriptive-name" style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1969)">Generated from star</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1987)">Typeset at 100.0 points</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 726)">Small</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1156)">Medium</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1586)">Large</text>
</g>
<g id="Guides">
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 696)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-S" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="696" y2="696"/>
<line id="Capline-S" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="625.541" y2="625.541"/>
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 1126)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-M" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1126" y2="1126"/>
<line id="Capline-M" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1055.54" y2="1055.54"/>
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 1556)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-L" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1556" y2="1556"/>
<line id="Capline-L" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1485.54" y2="1485.54"/>
<line id="left-margin-Ultralight-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="513.711" x2="513.711" y1="600.785" y2="720.121"/>
<line id="right-margin-Ultralight-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="605.711" x2="605.711" y1="600.785" y2="720.121"/>
<line id="left-margin-Regular-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="1403.84" x2="1403.84" y1="600.785" y2="720.121"/>
<line id="right-margin-Regular-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="1495.84" x2="1495.84" y1="600.785" y2="720.121"/>
<line id="left-margin-Black-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="2887.4" x2="2887.4" y1="600.785" y2="720.121"/>
<line id="right-margin-Black-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="2979.4" x2="2979.4" y1="600.785" y2="720.121"/>
</g>
<g id="Symbols">
<g id="Ultralight-S" transform="matrix(1 0 0 1 513.711 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="star">
<path id="Union" fill-rule="evenodd" clip-rule="evenodd" d="M40.1474 7.19511C42.8069 2.56477 49.715 2.71835 52.0929 7.65556L60.4222 24.9692C60.5656 25.2673 60.8597 25.4814 61.2046 25.527L80.3638 28.0314C85.8104 28.7434 88.1383 35.2693 84.4966 39.2356L84.126 39.61L70.1216 52.7946C69.8762 53.0264 69.7697 53.3611 69.8296 53.6818L73.3485 72.5227C74.394 78.1289 68.4515 82.3518 63.4881 79.6803L46.4852 70.5275C46.2579 70.4056 45.9928 70.3761 45.7477 70.4376L45.5119 70.5275L28.5089 79.6803C23.546 82.3504 17.6034 78.1283 18.6486 72.5227L22.1675 53.6818C22.2273 53.3602 22.1212 53.0262 21.8755 52.7946L7.87108 39.61C3.71413 35.6952 6.01254 28.7673 11.6333 28.0314L30.7925 25.527C31.1377 25.4819 31.4311 25.2674 31.5749 24.9692L39.9041 7.65556L40.1474 7.19511ZM46.7435 9.90166C46.2838 9.40706 45.4032 9.49143 45.0851 10.1525L36.7558 27.4624C35.7653 29.5205 33.7974 30.9329 31.5374 31.2283L12.3782 33.7365C11.518 33.8506 11.2239 34.8692 11.813 35.4248L25.8174 48.6093C27.4831 50.1784 28.2406 52.4849 27.8201 54.7374L24.3013 73.5784C24.1578 74.3558 25.0038 75.0377 25.7837 74.6191L42.7866 65.4663L43.554 65.1106C45.3864 64.4021 47.4564 64.5224 49.2104 65.4663L66.2134 74.6191C66.9936 75.0391 67.8397 74.3565 67.6958 73.5784L64.1769 54.7374C63.7561 52.4848 64.5149 50.178 66.1797 48.6093L80.1841 35.4248C80.7744 34.869 80.4806 33.8497 79.6188 33.7365L60.4596 31.2283C58.1998 30.9324 56.2313 29.5204 55.2412 27.4624L46.9119 10.1525L46.7435 9.90166Z" fill="black"/>
</g>
</svg>

</g>
<g id="Regular-S" transform="matrix(1 0 0 1 1403.84 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="star">
<path id="Union" fill-rule="evenodd" clip-rule="evenodd" d="M40.1474 7.19511C42.8069 2.56477 49.715 2.71835 52.0929 7.65556L60.4222 24.9692C60.5656 25.2673 60.8597 25.4814 61.2046 25.527L80.3638 28.0314C85.8104 28.7434 88.1383 35.2693 84.4966 39.2356L84.126 39.61L70.1216 52.7946C69.8762 53.0264 69.7697 53.3611 69.8296 53.6818L73.3485 72.5227C74.394 78.1289 68.4515 82.3518 63.4881 79.6803L46.4852 70.5275C46.2579 70.4056 45.9928 70.3761 45.7477 70.4376L45.5119 70.5275L28.5089 79.6803C23.546 82.3504 17.6034 78.1283 18.6486 72.5227L22.1675 53.6818C22.2273 53.3602 22.1212 53.0262 21.8755 52.7946L7.87108 39.61C3.71413 35.6952 6.01254 28.7673 11.6333 28.0314L30.7925 25.527C31.1377 25.4819 31.4311 25.2674 31.5749 24.9692L39.9041 7.65556L40.1474 7.19511ZM46.7435 9.90166C46.2838 9.40706 45.4032 9.49143 45.0851 10.1525L36.7558 27.4624C35.7653 29.5205 33.7974 30.9329 31.5374 31.2283L12.3782 33.7365C11.518 33.8506 11.2239 34.8692 11.813 35.4248L25.8174 48.6093C27.4831 50.1784 28.2406 52.4849 27.8201 54.7374L24.3013 73.5784C24.1578 74.3558 25.0038 75.0377 25.7837 74.6191L42.7866 65.4663L43.554 65.1106C45.3864 64.4021 47.4564 64.5224 49.2104 65.4663L66.2134 74.6191C66.9936 75.0391 67.8397 74.3565 67.6958 73.5784L64.1769 54.7374C63.7561 52.4848 64.5149 50.178 66.1797 48.6093L80.1841 35.4248C80.7744 34.869 80.4806 33.8497 79.6188 33.7365L60.4596 31.2283C58.1998 30.9324 56.2313 29.5204 55.2412 27.4624L46.9119 10.1525L46.7435 9.90166Z" fill="black"/>
</g>
</svg>

</g>
<g id="Black-S" transform="matrix(1 0 0 1 2887.4 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="star">
<path id="Union" fill-rule="evenodd" clip-rule="evenodd" d="M40.1474 7.19511C42.8069 2.56477 49.715 2.71835 52.0929 7.65556L60.4222 24.9692C60.5656 25.2673 60.8597 25.4814 61.2046 25.527L80.3638 28.0314C85.8104 28.7434 88.1383 35.2693 84.4966 39.2356L84.126 39.61L70.1216 52.7946C69.8762 53.0264 69.7697 53.3611 69.8296 53.6818L73.3485 72.5227C74.394 78.1289 68.4515 82.3518 63.4881 79.6803L46.4852 70.5275C46.2579 70.4056 45.9928 70.3761 45.7477 70.4376L45.5119 70.5275L28.5089 79.6803C23.546 82.3504 17.6034 78.1283 18.6486 72.5227L22.1675 53.6818C22.2273 53.3602 22.1212 53.0262 21.8755 52.7946L7.87108 39.61C3.71413 35.6952 6.01254 28.7673 11.6333 28.0314L30.7925 25.527C31.1377 25.4819 31.4311 25.2674 31.5749 24.9692L39.9041 7.65556L40.1474 7.19511ZM46.7435 9.90166C46.2838 9.40706 45.4032 9.49143 45.0851 10.1525L36.7558 27.4624C35.7653 29.5205 33.7974 30.9329 31.5374 31.2283L12.3782 33.7365C11.518 33.8506 11.2239 34.8692 11.813 35.4248L25.8174 48.6093C27.4831 50.1784 28.2406 52.4849 27.8201 54.7374L24.3013 73.5784C24.1578 74.3558 25.0038 75.0377 25.7837 74.6191L42.7866 65.4663L43.554 65.1106C45.3864 64.4021 47.4564 64.5224 49.2104 65.4663L66.2134 74.6191C66.9936 75.0391 67.8397 74.3565 67.6958 73.5784L64.1769 54.7374C63.7561 52.4848 64.5149 50.178 66.1797 48.6093L80.1841 35.4248C80.7744 34.869 80.4806 33.8497 79.6188 33.7365L60.4596 31.2283C58.1998 30.9324 56.2313 29.5204 55.2412 27.4624L46.9119 10.1525L46.7435 9.90166Z" fill="black"/>
</g>
</svg>

</g>
</g>
</svg>