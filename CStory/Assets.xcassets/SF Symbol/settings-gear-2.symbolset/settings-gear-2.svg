<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg
PUBLIC "-//W3C//DTD SVG 1.1//EN"
     "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
     <!--Created with SF Symbol Generator (v1.0.0)-->

<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="3300" height="2200">
<!--glyph: "settings-gear-2.medium", point size: 100.0-->
<style>.SFSymbolsPreviewWireframe {fill:none;opacity:1.0;stroke:black;stroke-width:0.5}
</style>
<g id="Notes">
<rect height="2200" id="artboard" style="fill:white;opacity:1" width="3300" x="0" y="0"/>
<line style="fill:none;stroke:black;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="292" y2="292"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 263 322)">Weight/Scale Variations</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 559.711 322)">Ultralight</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 856.422 322)">Thin</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1153.13 322)">Light</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1449.84 322)">Regular</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1746.56 322)">Medium</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2043.27 322)">Semibold</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2339.98 322)">Bold</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2636.69 322)">Heavy</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2933.4 322)">Black</text>
<line style="fill:none;stroke:black;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1903" y2="1903"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 263 1953)">Design Variations</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1971)">Symbols are supported in up to nine weights and three scales.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1989)">For optimal layout with text and other symbols, vertically align</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 2007)">symbols with the adjacent text.</text>
<line style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="776" x2="776" y1="1919" y2="1933"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 776 1953)">Margins</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 1971)">Leading and trailing margins on the left and right side of each symbol</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 1989)">can be adjusted by modifying the x-location of the margin guidelines.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 2007)">Modifications are automatically applied proportionally to all</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 2025)">scales and weights.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 1289 1953)">Exporting</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 1289 1971)">Symbols should be outlined when exporting to ensure the</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 1289 1989)">design is preserved when submitting to Xcode.</text>
<text id="template-version" style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1933)">Template v.5.0</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1951)">Requires Xcode 15 or greater</text>
<text id="descriptive-name" style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1969)">Generated from settings-gear-2</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1987)">Typeset at 100.0 points</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 726)">Small</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1156)">Medium</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1586)">Large</text>
</g>
<g id="Guides">
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 696)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-S" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="696" y2="696"/>
<line id="Capline-S" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="625.541" y2="625.541"/>
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 1126)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-M" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1126" y2="1126"/>
<line id="Capline-M" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1055.54" y2="1055.54"/>
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 1556)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-L" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1556" y2="1556"/>
<line id="Capline-L" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1485.54" y2="1485.54"/>
<line id="left-margin-Ultralight-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="513.711" x2="513.711" y1="600.785" y2="720.121"/>
<line id="right-margin-Ultralight-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="605.711" x2="605.711" y1="600.785" y2="720.121"/>
<line id="left-margin-Regular-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="1403.84" x2="1403.84" y1="600.785" y2="720.121"/>
<line id="right-margin-Regular-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="1495.84" x2="1495.84" y1="600.785" y2="720.121"/>
<line id="left-margin-Black-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="2887.4" x2="2887.4" y1="600.785" y2="720.121"/>
<line id="right-margin-Black-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="2979.4" x2="2979.4" y1="600.785" y2="720.121"/>
</g>
<g id="Symbols">
<g id="Ultralight-S" transform="matrix(1 0 0 1 513.711 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="settings-gear-2">
<g id="Union">
<path fill-rule="evenodd" clip-rule="evenodd" d="M46 32.5833C53.4099 32.5833 59.4167 38.5901 59.4167 46C59.4167 53.4099 53.4099 59.4167 46 59.4167C38.5901 59.4167 32.5833 53.4099 32.5833 46C32.5833 38.5901 38.5901 32.5833 46 32.5833ZM46 38.3333C41.7658 38.3333 38.3333 41.7658 38.3333 46C38.3333 50.2342 41.7658 53.6667 46 53.6667C50.2342 53.6667 53.6667 50.2342 53.6667 46C53.6667 41.7658 50.2342 38.3333 46 38.3333Z" fill="black"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M46 7.66667C49.1767 7.66667 52.1441 9.25399 53.9062 11.8968L55.3887 14.1242C56.9164 16.4149 59.7026 17.5204 62.3853 16.9019L63.849 16.5649C67.0946 15.8159 70.4967 16.7928 72.852 19.1479C75.2075 21.5033 76.1844 24.9053 75.4351 28.151L75.0981 29.6147C74.4796 32.2976 75.585 35.0838 77.8758 36.6113L80.1032 38.0937L80.5861 38.4382C82.9384 40.2276 84.3333 43.0216 84.3333 46C84.3333 48.9784 82.9384 51.7724 80.5861 53.5618L80.1032 53.9062L77.8758 55.3887C75.585 56.9164 74.4796 59.7025 75.0981 62.3853L75.4351 63.849C76.1844 67.0948 75.2074 70.4967 72.852 72.852C70.4967 75.2074 67.0948 76.1844 63.849 75.4351L62.3853 75.0981C59.7025 74.4796 56.9164 75.585 55.3887 77.8758L53.9062 80.1032C52.1441 82.7458 49.1766 84.3333 46 84.3333C43.0216 84.3333 40.2276 82.9384 38.4382 80.5861L38.0937 80.1032L36.6113 77.8758C35.0838 75.585 32.2976 74.4796 29.6147 75.0981L28.151 75.4351C24.9053 76.1844 21.5033 75.2075 19.1479 72.852C16.7928 70.4967 15.8159 67.0946 16.5649 63.849L16.9019 62.3853C17.5204 59.7026 16.4149 56.9164 14.1242 55.3887L11.8968 53.9062C9.25399 52.1441 7.66667 49.1767 7.66667 46C7.66667 42.8233 9.25399 39.8558 11.8968 38.0937L14.1242 36.6113C16.4149 35.0837 17.5204 32.2976 16.9019 29.6147L16.5649 28.151C15.816 24.9054 16.7927 21.5032 19.1479 19.1479C21.5032 16.7927 24.9054 15.816 28.151 16.5649L29.6147 16.9019C32.2976 17.5204 35.0837 16.4149 36.6113 14.1242L38.0937 11.8968C39.8558 9.25399 42.8233 7.66667 46 7.66667ZM46 13.4167C44.7461 13.4167 43.5738 14.0426 42.8779 15.0863L41.3955 17.3136C38.541 21.5947 33.3332 23.6629 28.3195 22.5059L26.8558 22.1652C25.5423 21.8625 24.1665 22.2603 23.2134 23.2134C22.2603 24.1665 21.8625 25.5423 22.1652 26.8558L22.5059 28.3195C23.6629 33.3332 21.5947 38.541 17.3136 41.3955L15.0863 42.8779C14.0426 43.5738 13.4167 44.7461 13.4167 46C13.4167 47.2539 14.0426 48.4262 15.0863 49.1221L17.3136 50.6045C21.5947 53.459 23.6629 58.6668 22.5059 63.6805L22.1652 65.1442C21.8625 66.4575 22.2601 67.8332 23.2134 68.7866C24.1665 69.7398 25.5424 70.1377 26.8558 69.8348L28.3195 69.4941C33.3332 68.3371 38.541 70.4051 41.3955 74.6864L42.8779 76.9137C43.5737 77.9572 44.746 78.5833 46 78.5833C47.254 78.5833 48.4263 77.9572 49.1221 76.9137L50.6045 74.6864C53.4591 70.4052 58.6668 68.3371 63.6805 69.4941L65.1442 69.8348C66.4575 70.1377 67.8332 69.74 68.7866 68.7866C69.74 67.8332 70.1377 66.4575 69.8348 65.1442L69.4941 63.6805C68.3371 58.6668 70.4052 53.4591 74.6864 50.6045L76.9137 49.1221C77.9572 48.4263 78.5833 47.254 78.5833 46C78.5833 44.746 77.9572 43.5737 76.9137 42.8779L74.6864 41.3955C70.4051 38.541 68.3371 33.3332 69.4941 28.3195L69.8348 26.8558C70.1377 25.5424 69.7398 24.1665 68.7866 23.2134C67.8332 22.2601 66.4575 21.8625 65.1442 22.1652L63.6805 22.5059C58.6668 23.6629 53.459 21.5947 50.6045 17.3136L49.1221 15.0863C48.4262 14.0426 47.2539 13.4167 46 13.4167Z" fill="black"/>
</g>
</g>
</svg>

</g>
<g id="Regular-S" transform="matrix(1 0 0 1 1403.84 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="settings-gear-2">
<g id="Union">
<path fill-rule="evenodd" clip-rule="evenodd" d="M46 32.5833C53.4099 32.5833 59.4167 38.5901 59.4167 46C59.4167 53.4099 53.4099 59.4167 46 59.4167C38.5901 59.4167 32.5833 53.4099 32.5833 46C32.5833 38.5901 38.5901 32.5833 46 32.5833ZM46 38.3333C41.7658 38.3333 38.3333 41.7658 38.3333 46C38.3333 50.2342 41.7658 53.6667 46 53.6667C50.2342 53.6667 53.6667 50.2342 53.6667 46C53.6667 41.7658 50.2342 38.3333 46 38.3333Z" fill="black"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M46 7.66667C49.1767 7.66667 52.1441 9.25399 53.9062 11.8968L55.3887 14.1242C56.9164 16.4149 59.7026 17.5204 62.3853 16.9019L63.849 16.5649C67.0946 15.8159 70.4967 16.7928 72.852 19.1479C75.2075 21.5033 76.1844 24.9053 75.4351 28.151L75.0981 29.6147C74.4796 32.2976 75.585 35.0838 77.8758 36.6113L80.1032 38.0937L80.5861 38.4382C82.9384 40.2276 84.3333 43.0216 84.3333 46C84.3333 48.9784 82.9384 51.7724 80.5861 53.5618L80.1032 53.9062L77.8758 55.3887C75.585 56.9164 74.4796 59.7025 75.0981 62.3853L75.4351 63.849C76.1844 67.0948 75.2074 70.4967 72.852 72.852C70.4967 75.2074 67.0948 76.1844 63.849 75.4351L62.3853 75.0981C59.7025 74.4796 56.9164 75.585 55.3887 77.8758L53.9062 80.1032C52.1441 82.7458 49.1766 84.3333 46 84.3333C43.0216 84.3333 40.2276 82.9384 38.4382 80.5861L38.0937 80.1032L36.6113 77.8758C35.0838 75.585 32.2976 74.4796 29.6147 75.0981L28.151 75.4351C24.9053 76.1844 21.5033 75.2075 19.1479 72.852C16.7928 70.4967 15.8159 67.0946 16.5649 63.849L16.9019 62.3853C17.5204 59.7026 16.4149 56.9164 14.1242 55.3887L11.8968 53.9062C9.25399 52.1441 7.66667 49.1767 7.66667 46C7.66667 42.8233 9.25399 39.8558 11.8968 38.0937L14.1242 36.6113C16.4149 35.0837 17.5204 32.2976 16.9019 29.6147L16.5649 28.151C15.816 24.9054 16.7927 21.5032 19.1479 19.1479C21.5032 16.7927 24.9054 15.816 28.151 16.5649L29.6147 16.9019C32.2976 17.5204 35.0837 16.4149 36.6113 14.1242L38.0937 11.8968C39.8558 9.25399 42.8233 7.66667 46 7.66667ZM46 13.4167C44.7461 13.4167 43.5738 14.0426 42.8779 15.0863L41.3955 17.3136C38.541 21.5947 33.3332 23.6629 28.3195 22.5059L26.8558 22.1652C25.5423 21.8625 24.1665 22.2603 23.2134 23.2134C22.2603 24.1665 21.8625 25.5423 22.1652 26.8558L22.5059 28.3195C23.6629 33.3332 21.5947 38.541 17.3136 41.3955L15.0863 42.8779C14.0426 43.5738 13.4167 44.7461 13.4167 46C13.4167 47.2539 14.0426 48.4262 15.0863 49.1221L17.3136 50.6045C21.5947 53.459 23.6629 58.6668 22.5059 63.6805L22.1652 65.1442C21.8625 66.4575 22.2601 67.8332 23.2134 68.7866C24.1665 69.7398 25.5424 70.1377 26.8558 69.8348L28.3195 69.4941C33.3332 68.3371 38.541 70.4051 41.3955 74.6864L42.8779 76.9137C43.5737 77.9572 44.746 78.5833 46 78.5833C47.254 78.5833 48.4263 77.9572 49.1221 76.9137L50.6045 74.6864C53.4591 70.4052 58.6668 68.3371 63.6805 69.4941L65.1442 69.8348C66.4575 70.1377 67.8332 69.74 68.7866 68.7866C69.74 67.8332 70.1377 66.4575 69.8348 65.1442L69.4941 63.6805C68.3371 58.6668 70.4052 53.4591 74.6864 50.6045L76.9137 49.1221C77.9572 48.4263 78.5833 47.254 78.5833 46C78.5833 44.746 77.9572 43.5737 76.9137 42.8779L74.6864 41.3955C70.4051 38.541 68.3371 33.3332 69.4941 28.3195L69.8348 26.8558C70.1377 25.5424 69.7398 24.1665 68.7866 23.2134C67.8332 22.2601 66.4575 21.8625 65.1442 22.1652L63.6805 22.5059C58.6668 23.6629 53.459 21.5947 50.6045 17.3136L49.1221 15.0863C48.4262 14.0426 47.2539 13.4167 46 13.4167Z" fill="black"/>
</g>
</g>
</svg>

</g>
<g id="Black-S" transform="matrix(1 0 0 1 2887.4 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="settings-gear-2">
<g id="Union">
<path fill-rule="evenodd" clip-rule="evenodd" d="M46 32.5833C53.4099 32.5833 59.4167 38.5901 59.4167 46C59.4167 53.4099 53.4099 59.4167 46 59.4167C38.5901 59.4167 32.5833 53.4099 32.5833 46C32.5833 38.5901 38.5901 32.5833 46 32.5833ZM46 38.3333C41.7658 38.3333 38.3333 41.7658 38.3333 46C38.3333 50.2342 41.7658 53.6667 46 53.6667C50.2342 53.6667 53.6667 50.2342 53.6667 46C53.6667 41.7658 50.2342 38.3333 46 38.3333Z" fill="black"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M46 7.66667C49.1767 7.66667 52.1441 9.25399 53.9062 11.8968L55.3887 14.1242C56.9164 16.4149 59.7026 17.5204 62.3853 16.9019L63.849 16.5649C67.0946 15.8159 70.4967 16.7928 72.852 19.1479C75.2075 21.5033 76.1844 24.9053 75.4351 28.151L75.0981 29.6147C74.4796 32.2976 75.585 35.0838 77.8758 36.6113L80.1032 38.0937L80.5861 38.4382C82.9384 40.2276 84.3333 43.0216 84.3333 46C84.3333 48.9784 82.9384 51.7724 80.5861 53.5618L80.1032 53.9062L77.8758 55.3887C75.585 56.9164 74.4796 59.7025 75.0981 62.3853L75.4351 63.849C76.1844 67.0948 75.2074 70.4967 72.852 72.852C70.4967 75.2074 67.0948 76.1844 63.849 75.4351L62.3853 75.0981C59.7025 74.4796 56.9164 75.585 55.3887 77.8758L53.9062 80.1032C52.1441 82.7458 49.1766 84.3333 46 84.3333C43.0216 84.3333 40.2276 82.9384 38.4382 80.5861L38.0937 80.1032L36.6113 77.8758C35.0838 75.585 32.2976 74.4796 29.6147 75.0981L28.151 75.4351C24.9053 76.1844 21.5033 75.2075 19.1479 72.852C16.7928 70.4967 15.8159 67.0946 16.5649 63.849L16.9019 62.3853C17.5204 59.7026 16.4149 56.9164 14.1242 55.3887L11.8968 53.9062C9.25399 52.1441 7.66667 49.1767 7.66667 46C7.66667 42.8233 9.25399 39.8558 11.8968 38.0937L14.1242 36.6113C16.4149 35.0837 17.5204 32.2976 16.9019 29.6147L16.5649 28.151C15.816 24.9054 16.7927 21.5032 19.1479 19.1479C21.5032 16.7927 24.9054 15.816 28.151 16.5649L29.6147 16.9019C32.2976 17.5204 35.0837 16.4149 36.6113 14.1242L38.0937 11.8968C39.8558 9.25399 42.8233 7.66667 46 7.66667ZM46 13.4167C44.7461 13.4167 43.5738 14.0426 42.8779 15.0863L41.3955 17.3136C38.541 21.5947 33.3332 23.6629 28.3195 22.5059L26.8558 22.1652C25.5423 21.8625 24.1665 22.2603 23.2134 23.2134C22.2603 24.1665 21.8625 25.5423 22.1652 26.8558L22.5059 28.3195C23.6629 33.3332 21.5947 38.541 17.3136 41.3955L15.0863 42.8779C14.0426 43.5738 13.4167 44.7461 13.4167 46C13.4167 47.2539 14.0426 48.4262 15.0863 49.1221L17.3136 50.6045C21.5947 53.459 23.6629 58.6668 22.5059 63.6805L22.1652 65.1442C21.8625 66.4575 22.2601 67.8332 23.2134 68.7866C24.1665 69.7398 25.5424 70.1377 26.8558 69.8348L28.3195 69.4941C33.3332 68.3371 38.541 70.4051 41.3955 74.6864L42.8779 76.9137C43.5737 77.9572 44.746 78.5833 46 78.5833C47.254 78.5833 48.4263 77.9572 49.1221 76.9137L50.6045 74.6864C53.4591 70.4052 58.6668 68.3371 63.6805 69.4941L65.1442 69.8348C66.4575 70.1377 67.8332 69.74 68.7866 68.7866C69.74 67.8332 70.1377 66.4575 69.8348 65.1442L69.4941 63.6805C68.3371 58.6668 70.4052 53.4591 74.6864 50.6045L76.9137 49.1221C77.9572 48.4263 78.5833 47.254 78.5833 46C78.5833 44.746 77.9572 43.5737 76.9137 42.8779L74.6864 41.3955C70.4051 38.541 68.3371 33.3332 69.4941 28.3195L69.8348 26.8558C70.1377 25.5424 69.7398 24.1665 68.7866 23.2134C67.8332 22.2601 66.4575 21.8625 65.1442 22.1652L63.6805 22.5059C58.6668 23.6629 53.459 21.5947 50.6045 17.3136L49.1221 15.0863C48.4262 14.0426 47.2539 13.4167 46 13.4167Z" fill="black"/>
</g>
</g>
</svg>

</g>
</g>
</svg>