<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg
PUBLIC "-//W3C//DTD SVG 1.1//EN"
     "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
     <!--Created with SF Symbol Generator (v1.0.0)-->

<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="3300" height="2200">
<!--glyph: "arrow-rotate-right-left.medium", point size: 100.0-->
<style>.SFSymbolsPreviewWireframe {fill:none;opacity:1.0;stroke:black;stroke-width:0.5}
</style>
<g id="Notes">
<rect height="2200" id="artboard" style="fill:white;opacity:1" width="3300" x="0" y="0"/>
<line style="fill:none;stroke:black;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="292" y2="292"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 263 322)">Weight/Scale Variations</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 559.711 322)">Ultralight</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 856.422 322)">Thin</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1153.13 322)">Light</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1449.84 322)">Regular</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1746.56 322)">Medium</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2043.27 322)">Semibold</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2339.98 322)">Bold</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2636.69 322)">Heavy</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2933.4 322)">Black</text>
<line style="fill:none;stroke:black;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1903" y2="1903"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 263 1953)">Design Variations</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1971)">Symbols are supported in up to nine weights and three scales.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1989)">For optimal layout with text and other symbols, vertically align</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 2007)">symbols with the adjacent text.</text>
<line style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="776" x2="776" y1="1919" y2="1933"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 776 1953)">Margins</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 1971)">Leading and trailing margins on the left and right side of each symbol</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 1989)">can be adjusted by modifying the x-location of the margin guidelines.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 2007)">Modifications are automatically applied proportionally to all</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 2025)">scales and weights.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 1289 1953)">Exporting</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 1289 1971)">Symbols should be outlined when exporting to ensure the</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 1289 1989)">design is preserved when submitting to Xcode.</text>
<text id="template-version" style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1933)">Template v.5.0</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1951)">Requires Xcode 15 or greater</text>
<text id="descriptive-name" style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1969)">Generated from arrow-rotate-right-left</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1987)">Typeset at 100.0 points</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 726)">Small</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1156)">Medium</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1586)">Large</text>
</g>
<g id="Guides">
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 696)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-S" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="696" y2="696"/>
<line id="Capline-S" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="625.541" y2="625.541"/>
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 1126)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-M" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1126" y2="1126"/>
<line id="Capline-M" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1055.54" y2="1055.54"/>
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 1556)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-L" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1556" y2="1556"/>
<line id="Capline-L" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1485.54" y2="1485.54"/>
<line id="left-margin-Ultralight-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="513.711" x2="513.711" y1="600.785" y2="720.121"/>
<line id="right-margin-Ultralight-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="605.711" x2="605.711" y1="600.785" y2="720.121"/>
<line id="left-margin-Regular-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="1403.84" x2="1403.84" y1="600.785" y2="720.121"/>
<line id="right-margin-Regular-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="1495.84" x2="1495.84" y1="600.785" y2="720.121"/>
<line id="left-margin-Black-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="2887.4" x2="2887.4" y1="600.785" y2="720.121"/>
<line id="right-margin-Black-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="2979.4" x2="2979.4" y1="600.785" y2="720.121"/>
</g>
<g id="Symbols">
<g id="Ultralight-S" transform="matrix(1 0 0 1 513.711 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="arrow-rotate-right-left">
<g id="Union">
<path d="M77.026 39.1943C78.6009 38.9991 80.0379 40.1163 80.2342 41.6912C80.4102 43.1037 80.5 44.5423 80.5 46C80.4999 65.0529 65.0531 80.499 46 80.5C36.3119 80.4997 27.3874 76.4941 21.0384 70.0482V77.625C21.0384 79.2128 19.7512 80.5 18.1634 80.5C16.5756 80.5 15.2884 79.2128 15.2884 77.625V62.2917C15.2884 60.7038 16.5756 59.4167 18.1634 59.4167H33.4967C35.0846 59.4167 36.3717 60.7038 36.3717 62.2917C36.3717 63.8795 35.0846 65.1667 33.4967 65.1667H24.3402C29.6709 71.0385 37.4802 74.7497 46 74.75C61.8774 74.749 74.7499 61.8773 74.75 46C74.75 44.7807 74.6758 43.5797 74.5291 42.4025C74.3328 40.8269 75.4504 39.3907 77.026 39.1943Z" fill="black"/>
<path d="M73.7917 11.5C75.3795 11.5 76.6667 12.7872 76.6667 14.375V29.7083C76.6667 31.2962 75.3795 32.5833 73.7917 32.5833H58.4583C56.8705 32.5833 55.5833 31.2962 55.5833 29.7083C55.5833 28.1205 56.8705 26.8333 58.4583 26.8333H67.6636C62.3321 20.9607 54.5208 17.25 46 17.25C30.1218 17.25 17.25 30.1218 17.25 46C17.25 47.2194 17.3241 48.4204 17.4709 49.5975C17.6673 51.173 16.5495 52.6092 14.974 52.8057C13.3989 53.0013 11.9622 51.8839 11.7658 50.3088C11.5896 48.8961 11.5 47.4578 11.5 46C11.5 26.9462 26.9462 11.5 46 11.5C55.6651 11.5 64.5704 15.4816 70.9167 21.8994V14.375C70.9167 12.7872 72.2038 11.5 73.7917 11.5Z" fill="black"/>
</g>
</g>
</svg>

</g>
<g id="Regular-S" transform="matrix(1 0 0 1 1403.84 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="arrow-rotate-right-left">
<g id="Union">
<path d="M77.026 39.1943C78.6009 38.9991 80.0379 40.1163 80.2342 41.6912C80.4102 43.1037 80.5 44.5423 80.5 46C80.4999 65.0529 65.0531 80.499 46 80.5C36.3119 80.4997 27.3874 76.4941 21.0384 70.0482V77.625C21.0384 79.2128 19.7512 80.5 18.1634 80.5C16.5756 80.5 15.2884 79.2128 15.2884 77.625V62.2917C15.2884 60.7038 16.5756 59.4167 18.1634 59.4167H33.4967C35.0846 59.4167 36.3717 60.7038 36.3717 62.2917C36.3717 63.8795 35.0846 65.1667 33.4967 65.1667H24.3402C29.6709 71.0385 37.4802 74.7497 46 74.75C61.8774 74.749 74.7499 61.8773 74.75 46C74.75 44.7807 74.6758 43.5797 74.5291 42.4025C74.3328 40.8269 75.4504 39.3907 77.026 39.1943Z" fill="black"/>
<path d="M73.7917 11.5C75.3795 11.5 76.6667 12.7872 76.6667 14.375V29.7083C76.6667 31.2962 75.3795 32.5833 73.7917 32.5833H58.4583C56.8705 32.5833 55.5833 31.2962 55.5833 29.7083C55.5833 28.1205 56.8705 26.8333 58.4583 26.8333H67.6636C62.3321 20.9607 54.5208 17.25 46 17.25C30.1218 17.25 17.25 30.1218 17.25 46C17.25 47.2194 17.3241 48.4204 17.4709 49.5975C17.6673 51.173 16.5495 52.6092 14.974 52.8057C13.3989 53.0013 11.9622 51.8839 11.7658 50.3088C11.5896 48.8961 11.5 47.4578 11.5 46C11.5 26.9462 26.9462 11.5 46 11.5C55.6651 11.5 64.5704 15.4816 70.9167 21.8994V14.375C70.9167 12.7872 72.2038 11.5 73.7917 11.5Z" fill="black"/>
</g>
</g>
</svg>

</g>
<g id="Black-S" transform="matrix(1 0 0 1 2887.4 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="arrow-rotate-right-left">
<g id="Union">
<path d="M77.026 39.1943C78.6009 38.9991 80.0379 40.1163 80.2342 41.6912C80.4102 43.1037 80.5 44.5423 80.5 46C80.4999 65.0529 65.0531 80.499 46 80.5C36.3119 80.4997 27.3874 76.4941 21.0384 70.0482V77.625C21.0384 79.2128 19.7512 80.5 18.1634 80.5C16.5756 80.5 15.2884 79.2128 15.2884 77.625V62.2917C15.2884 60.7038 16.5756 59.4167 18.1634 59.4167H33.4967C35.0846 59.4167 36.3717 60.7038 36.3717 62.2917C36.3717 63.8795 35.0846 65.1667 33.4967 65.1667H24.3402C29.6709 71.0385 37.4802 74.7497 46 74.75C61.8774 74.749 74.7499 61.8773 74.75 46C74.75 44.7807 74.6758 43.5797 74.5291 42.4025C74.3328 40.8269 75.4504 39.3907 77.026 39.1943Z" fill="black"/>
<path d="M73.7917 11.5C75.3795 11.5 76.6667 12.7872 76.6667 14.375V29.7083C76.6667 31.2962 75.3795 32.5833 73.7917 32.5833H58.4583C56.8705 32.5833 55.5833 31.2962 55.5833 29.7083C55.5833 28.1205 56.8705 26.8333 58.4583 26.8333H67.6636C62.3321 20.9607 54.5208 17.25 46 17.25C30.1218 17.25 17.25 30.1218 17.25 46C17.25 47.2194 17.3241 48.4204 17.4709 49.5975C17.6673 51.173 16.5495 52.6092 14.974 52.8057C13.3989 53.0013 11.9622 51.8839 11.7658 50.3088C11.5896 48.8961 11.5 47.4578 11.5 46C11.5 26.9462 26.9462 11.5 46 11.5C55.6651 11.5 64.5704 15.4816 70.9167 21.8994V14.375C70.9167 12.7872 72.2038 11.5 73.7917 11.5Z" fill="black"/>
</g>
</g>
</svg>

</g>
</g>
</svg>