import Foundation

/// 交易列表数据服务
///
/// 统一处理交易数据的获取、筛选、分组和转换，为所有需要显示交易列表的页面提供标准化的数据。
/// 支持时间范围筛选、卡片筛选等多种场景。
class TransactionListDataService {

  // MARK: - Singleton

  static let shared = TransactionListDataService()

  private init() {}

  // MARK: - Data Structures

  /// 收支统计结果
  struct IncomeExpenseResult {
    /// 总收入
    let totalIncome: Double
    /// 总支出
    let totalExpense: Double
    /// 净收入（收入-支出）
    let netIncome: Double
    /// 货币符号
    let currencySymbol: String
    /// 统计的交易数量
    let transactionCount: Int
  }

  /// 交易列表综合结果（包含列表数据和统计数据）
  struct TransactionListResult {
    /// 交易日期分组数据
    let transactionDayGroups: [TransactionDayGroupWithRowVM]
    /// 收支统计数据
    let incomeExpenseStatistics: IncomeExpenseResult
    /// 总交易数量
    let totalTransactionCount: Int
    /// 涉及的日期数量
    let dayCount: Int
  }

  // MARK: - Public Methods

  /// 获取交易列表数据和收支统计（综合方法，推荐使用）
  /// - Parameters:
  ///   - dataManager: 数据管理器
  ///   - dateRange: 时间范围（可选，不传则获取所有交易）
  ///   - recentDays: 最近几天（可选，如果设置则忽略 dateRange，使用 recentTransactions 数据源优化性能）
  ///   - cardId: 指定卡片ID（可选，不传则获取所有卡片的交易）
  ///   - targetCurrency: 目标货币代码（可选，不传则使用本位货币）
  ///   - showBalance: 是否显示余额（用于卡片详情页面）
  ///   - includeSystemTransactions: 是否包含系统交易（创建卡片、调整余额等）
  ///   - onTransactionTap: 交易点击回调
  /// - Returns: 包含交易列表和收支统计的综合结果
  func getTransactionListWithStatistics(
    dataManager: DataManagement,
    dateRange: (start: Date, end: Date)? = nil,
    recentDays: Int? = nil,
    cardId: UUID? = nil,
    targetCurrency: String? = nil,
    showBalance: Bool = false,
    includeSystemTransactions: Bool = false,
    onTransactionTap: ((TransactionModel) -> Void)? = nil
  ) -> TransactionListResult {

    // 确定目标货币：优先使用指定货币，否则使用本位货币
    let targetCurrencyCode =
      targetCurrency ?? (UserDefaults.standard.string(forKey: "baseCurrencyCode") ?? "CNY")
    let currencySymbol =
      dataManager.currencies.first { $0.code == targetCurrencyCode }?.symbol ?? "¥"

    // 1. 获取基础交易数据和时间筛选
    let timeFilteredTransactions: [TransactionModel]

    if let recentDays = recentDays {
      // 使用最近几天模式（性能优化，使用 recentTransactions）
      let calendar = Calendar.current
      let today = Date()
      guard let startDate = calendar.date(byAdding: .day, value: -recentDays, to: today) else {
        return TransactionListResult(
          transactionDayGroups: [],
          incomeExpenseStatistics: IncomeExpenseResult(
            totalIncome: 0, totalExpense: 0, netIncome: 0,
            currencySymbol: currencySymbol, transactionCount: 0
          ),
          totalTransactionCount: 0,
          dayCount: 0
        )
      }

      timeFilteredTransactions = dataManager.recentTransactions.filter { transaction in
        transaction.transactionDate >= startDate && transaction.transactionDate <= today
      }
    } else if let dateRange = dateRange {
      // 使用指定时间范围（使用 allTransactions）
      timeFilteredTransactions = dataManager.allTransactions.filter { transaction in
        transaction.transactionDate >= dateRange.start
          && transaction.transactionDate < dateRange.end
      }
    } else {
      // 获取所有交易
      timeFilteredTransactions = dataManager.allTransactions
    }

    // 2. 应用卡片筛选
    let cardFilteredTransactions: [TransactionModel]
    if let cardId = cardId {
      cardFilteredTransactions = timeFilteredTransactions.filter { transaction in
        transaction.fromCardId == cardId || transaction.toCardId == cardId
      }
    } else {
      cardFilteredTransactions = timeFilteredTransactions
    }

    // 3. 分别处理收支统计和交易列表显示

    // 3.1 收支统计：只包含真实交易，排除系统交易
    let statisticsTransactions = cardFilteredTransactions.filter { transaction in
      [TransactionType.income, .expense, .transfer, .refund].contains(transaction.transactionType)
    }

    // 3.2 交易列表显示：根据参数决定是否包含系统交易
    let displayTransactions: [TransactionModel]
    if includeSystemTransactions {
      // 包含系统交易：显示所有交易类型
      displayTransactions = cardFilteredTransactions.filter { transaction in
        [TransactionType.income, .expense, .transfer, .refund, .createCard, .adjustCard].contains(
          transaction.transactionType)
      }
    } else {
      // 不包含系统交易：只显示真实交易
      displayTransactions = statisticsTransactions
    }

    // 4. 按日期分组（使用显示交易数据）
    let calendar = Calendar.current
    let groupedByDate = Dictionary(grouping: displayTransactions) { transaction in
      calendar.startOfDay(for: transaction.transactionDate)
    }

    // 5. 分别计算收支统计和创建UI数据

    // 5.1 计算收支统计（只使用真实交易，不包含系统交易）
    var totalIncome: Double = 0
    var totalExpense: Double = 0

    for transaction in statisticsTransactions {
      let amounts = TransactionCalculationService.shared.getTransactionIncomeExpenseAmounts(
        transaction: transaction,
        targetCurrency: targetCurrencyCode
      )
      totalIncome += amounts.income
      totalExpense += amounts.expense
    }

    // 5.2 创建UI数据（使用显示交易数据，可能包含系统交易）
    let transactionDayGroups = groupedByDate.map { date, transactions in
      // 计算当日收支（只计算真实交易的收支）
      let dayStatisticsTransactions = transactions.filter { transaction in
        [TransactionType.income, .expense, .transfer, .refund].contains(transaction.transactionType)
      }

      let (dayIncome, dayExpense) = dayStatisticsTransactions.reduce((0.0, 0.0)) {
        result, transaction in
        let amounts = TransactionCalculationService.shared.getTransactionIncomeExpenseAmounts(
          transaction: transaction,
          targetCurrency: targetCurrencyCode
        )
        return (result.0 + amounts.income, result.1 + amounts.expense)
      }

      return createTransactionDayGroup(
        date: date,
        transactions: transactions,
        dataManager: dataManager,
        cardId: cardId,
        precomputedIncome: dayIncome,
        precomputedExpense: dayExpense,
        showBalance: showBalance,
        onTransactionTap: onTransactionTap
      )
    }
    .sorted { $0.date > $1.date }
    .map { $0.group }

    // 6. 创建收支统计结果（基于真实交易）
    let incomeExpenseStatistics = IncomeExpenseResult(
      totalIncome: totalIncome,
      totalExpense: totalExpense,
      netIncome: totalIncome - totalExpense,
      currencySymbol: currencySymbol,
      transactionCount: statisticsTransactions.count  // 只计算真实交易数量
    )

    return TransactionListResult(
      transactionDayGroups: transactionDayGroups,
      incomeExpenseStatistics: incomeExpenseStatistics,
      totalTransactionCount: displayTransactions.count,  // 显示的交易总数（可能包含系统交易）
      dayCount: groupedByDate.count
    )
  }

  /// 获取交易列表数据
  /// - Parameters:
  ///   - dataManager: 数据管理器
  ///   - dateRange: 时间范围（可选，不传则获取所有交易）
  ///   - recentDays: 最近几天（可选，如果设置则忽略 dateRange，使用 recentTransactions 数据源优化性能）
  ///   - cardId: 指定卡片ID（可选，不传则获取所有卡片的交易）
  ///   - targetCurrency: 目标货币代码（可选，不传则使用本位货币）
  ///   - showBalance: 是否显示余额（用于卡片详情页面）
  ///   - includeSystemTransactions: 是否在列表中显示系统交易（不影响收支统计）
  ///   - onTransactionTap: 交易点击回调
  /// - Returns: 格式化的交易日期分组数据
  func getTransactionListData(
    dataManager: DataManagement,
    dateRange: (start: Date, end: Date)? = nil,
    recentDays: Int? = nil,
    cardId: UUID? = nil,
    targetCurrency: String? = nil,
    showBalance: Bool = false,
    includeSystemTransactions: Bool = false,
    onTransactionTap: ((TransactionModel) -> Void)? = nil
  ) -> [TransactionDayGroupWithRowVM] {

    // 使用综合方法，只返回交易列表部分
    return getTransactionListWithStatistics(
      dataManager: dataManager,
      dateRange: dateRange,
      recentDays: recentDays,
      cardId: cardId,
      targetCurrency: targetCurrency,
      showBalance: showBalance,
      includeSystemTransactions: includeSystemTransactions,
      onTransactionTap: onTransactionTap
    ).transactionDayGroups
  }

  /// 计算收支统计
  /// - Parameters:
  ///   - dataManager: 数据管理器
  ///   - dateRange: 时间范围（可选，不传则获取所有交易）
  ///   - recentDays: 最近几天（可选，如果设置则忽略 dateRange）
  ///   - cardId: 指定卡片ID（可选，不传则获取所有卡片的交易）
  ///   - targetCurrency: 目标货币代码（可选，不传则使用本位货币）
  /// - Returns: 收支统计结果
  func calculateIncomeExpenseStatistics(
    dataManager: DataManagement,
    dateRange: (start: Date, end: Date)? = nil,
    recentDays: Int? = nil,
    cardId: UUID? = nil,
    targetCurrency: String? = nil
  ) -> IncomeExpenseResult {

    // 使用综合方法，只返回收支统计部分
    return getTransactionListWithStatistics(
      dataManager: dataManager,
      dateRange: dateRange,
      recentDays: recentDays,
      cardId: cardId,
      targetCurrency: targetCurrency,
      showBalance: false  // 统计方法不需要余额
    ).incomeExpenseStatistics
  }

  // MARK: - Private Methods

  /// 创建交易日期分组
  /// - Parameters:
  ///   - date: 日期
  ///   - transactions: 当日交易列表
  ///   - dataManager: 数据管理器
  ///   - cardId: 卡片ID（可选）
  ///   - precomputedIncome: 预计算的收入（可选，传入则跳过计算）
  ///   - precomputedExpense: 预计算的支出（可选，传入则跳过计算）
  ///   - showBalance: 是否显示余额（用于卡片详情页面）
  ///   - onTransactionTap: 交易点击回调
  /// - Returns: 日期分组数据
  private func createTransactionDayGroup(
    date: Date,
    transactions: [TransactionModel],
    dataManager: DataManagement,
    cardId: UUID? = nil,
    precomputedIncome: Double? = nil,
    precomputedExpense: Double? = nil,
    showBalance: Bool = false,
    onTransactionTap: ((TransactionModel) -> Void)? = nil
  ) -> (date: Date, group: TransactionDayGroupWithRowVM) {

    // 确定当日收支：使用预计算数据或现场计算
    let (dayIncome, dayExpense): (Double, Double)

    if let precomputedIncome = precomputedIncome, let precomputedExpense = precomputedExpense {
      // 使用预计算的收支数据（性能优化路径）
      dayIncome = precomputedIncome
      dayExpense = precomputedExpense
    } else {
      // 现场计算当日收支（兼容性路径）
      let baseCurrencyCode = UserDefaults.standard.string(forKey: "baseCurrencyCode") ?? "CNY"
      let result = transactions.reduce((0.0, 0.0)) { result, transaction in
        let amounts = TransactionCalculationService.shared.getTransactionIncomeExpenseAmounts(
          transaction: transaction,
          targetCurrency: baseCurrencyCode
        )
        return (result.0 + amounts.income, result.1 + amounts.expense)
      }
      dayIncome = result.0
      dayExpense = result.1
    }

    // 创建交易行视图模型
    let transactionRowVMs =
      transactions
      .sorted { $0.transactionDate > $1.transactionDate }
      .map { transaction in
        let relatedCard =
          dataManager.findCard(by: transaction.fromCardId)
          ?? dataManager.findCard(by: transaction.toCardId)
        let categoryInfo = dataManager.getCategoryInfo(for: transaction.transactionCategoryId)

        // 确定显示上下文
        let displayContext: TransactionDisplayContext = cardId != nil ? .cardDetail : .list

        let rowVM = TransactionRowVM(
          transaction: transaction,
          relatedCard: relatedCard,
          categoryInfo: categoryInfo,
          displayContext: displayContext,
          onTap: {
            // 移除重复的震动反馈，让业务层统一处理
            onTransactionTap?(transaction)
          }
        )

        // 根据 showBalance 参数决定是否显示余额
        if showBalance, let card = relatedCard {
          let balanceAfterTransaction = BalanceRecalculationService.shared
            .calculateBalanceAtTimePoint(
              card: card,
              targetDate: transaction.transactionDate,
              transactions: dataManager.allTransactions,
              currencies: dataManager.currencies
            )

          rowVM.balanceText =
            "余额 \(card.symbol)\(NumberFormatService.shared.formatAmount(balanceAfterTransaction))"
        }

        return rowVM
      }

    let group = TransactionDayGroupWithRowVM(
      dateText: DateFormattingHelper.shared.formatDateHeader(date: date),
      dayIncome: dayIncome,
      dayExpense: dayExpense,
      transactionRowVMs: transactionRowVMs
    )

    return (date: date, group: group)
  }
}
