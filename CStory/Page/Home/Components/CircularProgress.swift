import SwiftUI

/// 多圆环进度组件
/// 专门为首页财务健康度指标设计的轻量级组件
struct CircularProgress: View {

  // MARK: - Properties

  let savingsRatio: Double
  let creditRatio: Double
  let cashFlowScore: Double
  let size: CGFloat

  // MARK: - Constants

  private let lineWidth: CGFloat = 8  // 减小线宽以腾出空间
  private let spacing: CGFloat = 12  // 增加间距

  // 固定的颜色配置
  private let savingsColor: Color = .accentColor
  private let creditColor: Color = .green
  private let cashFlowColor: Color = .orange

  // MARK: - Computed Properties

  private var outerRingSize: CGFloat { size }
  private var middleRingSize: CGFloat { size - (lineWidth + spacing) * 2 }
  private var innerRingSize: CGFloat { size - (lineWidth + spacing) * 4 }

  // MARK: - Body

  var body: some View {
    ZStack {
      // 外环 - 储蓄净值占比
      circleRing(
        progress: savingsRatio,
        color: savingsColor,
        size: outerRingSize
      )

      // 中环 - 信用额度可用率
      circleRing(
        progress: creditRatio,
        color: creditColor,
        size: middleRingSize
      )

      // 内环 - 现金流健康度
      circleRing(
        progress: cashFlowScore,
        color: cashFlowColor,
        size: innerRingSize
      )
    }
    .frame(width: size, height: size)
  }

  // MARK: - Helper Views

  @ViewBuilder
  private func circleRing(progress: Double, color: Color, size: CGFloat) -> some View {
    ZStack {
      // 背景圆环
      Circle()
        .stroke(color.opacity(0.12), lineWidth: lineWidth)
        .frame(width: size, height: size)

      // 进度圆环
      Circle()
        .trim(from: 0.0, to: progress)
        .stroke(
          color,
          style: StrokeStyle(lineWidth: lineWidth, lineCap: .round)
        )
        .frame(width: size, height: size)
        .rotationEffect(.degrees(-90))  // 从顶部开始
        .animation(.easeInOut(duration: 0.8), value: progress)
    }
  }
}

// MARK: - Preview

#Preview {
  VStack(spacing: 30) {
    Text("圆环进度组件")
      .font(.title2)
      .fontWeight(.bold)

    CircularProgress(
      savingsRatio: 0.75,
      creditRatio: 0.60,
      cashFlowScore: 0.85,
      size: 90
    )

    CircularProgress(
      savingsRatio: 0.30,
      creditRatio: 0.90,
      cashFlowScore: 0.45,
      size: 120
    )
  }
  .padding()
}
