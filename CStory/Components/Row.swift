//
//  Row.swift
//  CStory
//
//  Created by nzue on 2025/8/20.
//

import SwiftUI

struct Row: View {
  // 可选参数，传值就显示，不传就不显示
  let leftIconName: String?
  let imageName: String?
  let imageData: Data?
  let emoji: String?
  let leftMainText: String?
  let leftSubText: String?
  let tagText: String?
  let rightMainText: String?
  let rightSubText: String?
  let rightMainIcon: String?
  let rightSubIcon: String?
  let selectBorder: Bool?

  // 三选项切换组件参数
  let switcherOptions: [String]?
  let selectedSwitcherIndex: Int?
  let onSwitcherTap: ((Int) -> Void)?

  // 回调函数
  let onLeftIconTap: (() -> Void)?
  let onRightMainIconTap: (() -> Void)?
  let onRightSubIconTap: (() -> Void)?
  let onRowTap: (() -> Void)?

  // 初始化方法，所有参数都是可选的
  init(
    leftIconName: String? = nil,
    imageName: String? = nil,
    imageData: Data? = nil,
    emoji: String? = nil,
    leftMainText: String? = nil,
    leftSubText: String? = nil,
    tagText: String? = nil,
    rightMainText: String? = nil,
    rightSubText: String? = nil,
    rightMainIcon: String? = nil,
    rightSubIcon: String? = nil,
    selectBorder: Bool? = nil,
    switcherOptions: [String]? = nil,
    selectedSwitcherIndex: Int? = nil,
    onSwitcherTap: ((Int) -> Void)? = nil,
    onLeftIconTap: (() -> Void)? = nil,
    onRightMainIconTap: (() -> Void)? = nil,
    onRightSubIconTap: (() -> Void)? = nil,
    onRowTap: (() -> Void)? = nil
  ) {
    self.leftIconName = leftIconName
    self.imageName = imageName
    self.imageData = imageData
    self.emoji = emoji
    self.leftMainText = leftMainText
    self.leftSubText = leftSubText
    self.tagText = tagText
    self.rightMainText = rightMainText
    self.rightSubText = rightSubText
    self.rightMainIcon = rightMainIcon
    self.rightSubIcon = rightSubIcon
    self.selectBorder = selectBorder
    self.switcherOptions = switcherOptions
    self.selectedSwitcherIndex = selectedSwitcherIndex
    self.onSwitcherTap = onSwitcherTap
    self.onLeftIconTap = onLeftIconTap
    self.onRightMainIconTap = onRightMainIconTap
    self.onRightSubIconTap = onRightSubIconTap
    self.onRowTap = onRowTap
  }

  var body: some View {
    HStack(alignment: .center) {
      HStack(spacing: 4) {
        HStack(spacing: 12.0) {
          if let leftIconName = leftIconName {
            Image(leftIconName)
              .font(.system(size: 16, weight: .regular))
              .foregroundColor(.cBlack.opacity(0.4))
              .frame(width: 24, height: 24)
              .onTapGesture {
                onLeftIconTap?()
              }
          }
          // 图片显示 - 使用私有的通用图片组件
          if imageData != nil || imageName != nil || emoji != nil {
            UniversalImageView(
              imageName: imageName,
              imageData: imageData,
              emoji: emoji,
              size: 44,
              cornerRadius: 16
            )
          }
          VStack(alignment: .leading, spacing: 4.0) {
            HStack(alignment: .center) {
              // 只有传入 leftMainText 时才显示左主文案
              if let leftMainText = leftMainText {
                Text(leftMainText)
                  .font(.system(size: 14, weight: .medium))
                  .foregroundColor(.cBlack)
              }
              // 只有传入 tagText 时才显示标签文案
              if let tagText = tagText {
                Text(tagText)
                  .font(.system(size: 10, weight: .medium))
                  .foregroundColor(.cWhite)
                  .padding(.horizontal, 4)
                  .padding(.vertical, 2)
                  .background(.cAccentRed)
                  .cornerRadius(12)
              }
            }
            // 只有传入 leftSubText 时才显示左副文案
            if let leftSubText = leftSubText {
              Text(leftSubText)
                .font(.system(size: 12, weight: .regular))
                .foregroundColor(.cBlack.opacity(0.4))
            }
          }
          .padding(.leading, 4.0)
        }
      }
      Spacer()
      HStack(alignment: .center, spacing: 0.0) {
        VStack(alignment: .trailing, spacing: 4.0) {
          // 只有传入 rightMainText 时才显示右主文案
          if let rightMainText = rightMainText {
            Text(rightMainText)
              .font(.system(size: 14, weight: .medium))
              .foregroundColor(.cBlack)
          }
          // 只有传入 rightSubText 时才显示右副文案
          if let rightSubText = rightSubText {
            Text(rightSubText)
              .font(.system(size: 12, weight: .regular))
              .foregroundColor(.cBlack.opacity(0.4))
          }
        }
        .padding(.trailing, 8.0)
        HStack(spacing: 8.0) {
          if let rightMainIcon = rightMainIcon {

            Image(rightMainIcon)
              .font(.system(size: 16, weight: .regular))
              .foregroundColor(.cBlack.opacity(0.4))
              .frame(width: 24, height: 24)
              .onTapGesture {
                onRightMainIconTap?()
              }

          }
          if let rightSubIcon = rightSubIcon {

            Image(rightSubIcon)
              .font(.system(size: 16, weight: .regular))
              .foregroundColor(.cBlack.opacity(0.4))
              .frame(width: 24, height: 24)
              .onTapGesture {
                onRightSubIconTap?()
              }

          }

          // 三选项切换组件
          if let switcherOptions = switcherOptions, let selectedIndex = selectedSwitcherIndex {
            ThreeOptionSwitcher(
              options: switcherOptions,
              selectedIndex: selectedIndex,
              onTap: onSwitcherTap
            )
          }
        }

      }

    }
    .padding(.vertical, 8)
    .padding(.horizontal, 12)
    .frame(minHeight: 56)
    .background(.cWhite.opacity(0.5))
    .cornerRadius(24)
    .overlay(
      RoundedRectangle(cornerRadius: 24)
        .strokeBorder(.cAccentBlue, lineWidth: selectBorder == true ? 1 : 0)
    )
    .onTapGesture {
      onRowTap?()
    }

  }
}

#Preview {
  VStack(spacing: 12.0) {
    Spacer()
    // 示例：传入所有参数和回调函数
    Row(
      leftIconName: "circle-placeholder-on",
      imageName: "爱西爱西爱银行",
      leftMainText: "左主文案",
      leftSubText: "左副文案",
      tagText: "标签文案",
      rightMainText: "右主文案",
      rightSubText: "右副文案",
      rightMainIcon: "highlight",
      rightSubIcon: "circle-placeholder-on",
      onLeftIconTap: {
        print("左侧图标被点击")
      },
      onRightMainIconTap: {
        print("右主图标被点击")
      },
      onRightSubIconTap: {
        print("右副图标被点击")
      },
      onRowTap: {
        print("整行被点击")
      }
    )

    //选择卡片行
    Row(
      imageName: "爱西爱西爱银行",
      leftMainText: "左主文案",
      leftSubText: "左副文案",
      rightMainText: "右主文案",
      selectBorder: true
    )
    //选择货币行
    Row(
      leftMainText: "左主文案",
      leftSubText: "左副文案",
      rightMainText: "右主文案",
    )
    //货币设置行
    Row(
      leftIconName: "circle-placeholder-on",
      leftMainText: "左主文案",
      leftSubText: "左副文案",
      rightMainText: "右主文案",
      rightSubIcon: "chevron-right",
      onRowTap: {
        print("整行被点击")
      }
    )
    // 操作选项行
    Row(
      leftIconName: "circle-placeholder-on",
      leftMainText: "左主文案",
      onRowTap: {
        print("整行被点击")
      }
    )

    // emoji行
    Row(
      emoji: "🏦",
      leftMainText: "银行emoji",
      leftSubText: "使用emoji显示",
      rightMainText: "右主文案"
    )

    // 三选项切换行
    Row(
      leftMainText: "记账模式",
      leftSubText: "选择记账方式",
      switcherOptions: ["支出", "收入", "转账"],
      selectedSwitcherIndex: 0,
      onSwitcherTap: { index in
        print("选择了选项: \(index)")
      }
    )

    Spacer()
  }
  .padding(.horizontal, 16)
  .background(.cBeige)

}

// MARK: - Private Components

/// 私有的通用图片显示组件
/// 支持显示图片名称、Data数据和emoji，传什么值就显示什么
private struct UniversalImageView: View {
  let imageName: String?
  let imageData: Data?
  let emoji: String?
  let size: CGFloat
  let cornerRadius: CGFloat

  var body: some View {
    Group {
      if let emoji = emoji {
        // 显示emoji
        Text(emoji)
          .font(.system(size: size * 0.6))
          .frame(width: size, height: size)
          .background(.cAccentBlue.opacity(0.1))
          .cornerRadius(cornerRadius)
      } else if let imageData = imageData, let uiImage = UIImage(data: imageData) {
        // 显示Data类型的图片
        Image(uiImage: uiImage)
          .resizable()
          .aspectRatio(contentMode: .fill)
          .frame(width: size, height: size)
          .clipped()
          .cornerRadius(cornerRadius)
      } else if let imageName = imageName {
        // 显示图片名称
        Image(imageName)
          .resizable()
          .frame(width: size, height: size)
          .cornerRadius(cornerRadius)
      } else {
        // 默认占位符
        Image("question")
          .resizable()
          .frame(width: size, height: size)
          .cornerRadius(cornerRadius)
          .opacity(0.6)
      }
    }
  }
}

/// 私有的三选项切换组件
/// 支持传递三个文字，宽度根据文字宽度自适应，类似手动记账的收入支出切换
private struct ThreeOptionSwitcher: View {
  let options: [String]
  let selectedIndex: Int
  let onTap: ((Int) -> Void)?

  @Namespace private var switcherAnimation

  var body: some View {
    HStack(spacing: 2) {
      ForEach(Array(options.enumerated()), id: \.offset) { index, option in
        Button(action: {
          onTap?(index)
        }) {
          Text(option)
            .font(.system(size: 14, weight: .medium))
            .foregroundColor(selectedIndex == index ? .cWhite : .cBlack.opacity(0.6))
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
              ZStack {
                if selectedIndex == index {
                  Color.cAccentBlue
                    .cornerRadius(12)
                    .matchedGeometryEffect(id: "switcherBackground", in: switcherAnimation)
                }
              }
            )
        }
        .buttonStyle(PlainButtonStyle())
      }
    }
    .background(.cWhite.opacity(0.5))
    .cornerRadius(14)
    .animation(.spring(response: 0.4, dampingFraction: 0.8), value: selectedIndex)
  }
}
