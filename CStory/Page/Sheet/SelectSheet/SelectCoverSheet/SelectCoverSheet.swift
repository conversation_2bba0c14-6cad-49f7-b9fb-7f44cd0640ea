//
//  BackgroundOptionsSheet.swift
//  CStory
//
//  Created by NZUE on 2025/12/19.
//

import SwiftUI

/// 卡片背景选择 Sheet
struct SelectCoverSheet: View {
  // MARK: - 属性

  /// ViewModel
  @ObservedObject private var viewModel: SelectCoverSheetVM

  // MARK: - 初始化
  init(viewModel: SelectCoverSheetVM) {
    self.viewModel = viewModel
  }

  // MARK: - Body

  var body: some View {
    VStack(spacing: 12) {
      // 标题栏
      SheetTitle(
        title: "选择背景样式", button: "circle-x",
        rightButtonAction: {
          viewModel.handleDismiss()
        })

      // 主内容区
      ScrollView {
        LazyVStack(alignment: .leading, spacing: 20) {
          // 预设背景选择区域
          VStack(alignment: .leading, spacing: 12) {
            Text("预设背景")
              .font(.system(size: 16, weight: .medium))
              .foregroundColor(.cBlack)
              .padding(.horizontal, 16)

            // 使用LazyVGrid显示预设背景
            LazyVGrid(
              columns: [
                GridItem(.flexible(), spacing: 12),
                GridItem(.flexible(), spacing: 12),
                GridItem(.flexible(), spacing: 12),
              ], spacing: 20
            ) {
              ForEach(0..<viewModel.presets.count, id: \.self) { index in
                let preset = viewModel.presets[index]
                CardPreviewItem(
                  title: preset.title,
                  imageName: preset.imageName,
                  isSelected: viewModel.isBackgroundSelected(preset),
                  width: viewModel.cardWidth
                )
                .onTapGesture {
                  viewModel.handleBackgroundSelection(preset)
                }
              }
            }
            .padding(.horizontal, 16)
          }
        }
        .padding(.vertical, 12)
      }
    }

    .edgesIgnoringSafeArea(.bottom)
  }
}

// MARK: - 卡片预览项

/// 卡片背景预览项
private struct CardPreviewItem: View {
  var title: String
  var imageName: String
  var isSelected: Bool
  var width: CGFloat

  var body: some View {
    VStack(spacing: 8) {
      // 图片
      Image(imageName)
        .resizable()
        .aspectRatio(contentMode: .fill)
        .frame(width: width, height: width * 0.6)
        .clipShape(RoundedRectangle(cornerRadius: 12))
        .overlay(
          RoundedRectangle(cornerRadius: 12)
            .strokeBorder(.cAccentBlue, lineWidth: isSelected ? 2 : 0)
        )
        .overlay(
          // 勾选标记
          isSelected
            ? Image("circle-check")
              .foregroundColor(.cAccentBlue)
              .background(Circle().fill(.white))
              .font(.system(size: 18, weight: .bold))
              .padding(8)
              .transition(.opacity) : nil,
          alignment: .topTrailing
        )

      // 标题
      Text(title)
        .font(.system(size: 12, weight: .medium))
        .foregroundColor(.cBlack)
        .frame(maxWidth: .infinity, alignment: .center)
    }
    .padding(4)
    .background(isSelected ? .cAccentBlue.opacity(0.05) : .clear)
    .cornerRadius(12)
    .animation(.easeInOut(duration: 0.2), value: isSelected)
  }
}

// MARK: - 预览代码 (Preview Provider)

#if DEBUG
  struct SelectCoverSheet_Previews: PreviewProvider {
    static var previews: some View {
      SelectCoverPreviewContainer()
    }
  }

  struct SelectCoverPreviewContainer: View {
    @State private var showSheet = false
    @State private var selectedType: CardCoverType = .card1
    @State private var isDarkBackground = false

    var body: some View {
      VStack {
        Button("选择背景") {
          showSheet = true
        }
        .padding()
        .background(.cAccentBlue)
        .foregroundColor(.white)
        .cornerRadius(12)

        Text("当前背景: \(selectedType.rawValue)")
          .padding()
        Text("深色背景: \(isDarkBackground ? "是" : "否")")
          .padding()
      }
      .frame(maxWidth: .infinity, maxHeight: .infinity)
      .background(.cLightBlue)
      .floatingSheet(
        isPresented: $showSheet,
        config: SheetBase(
          maxDetent: .fraction(0.8),
          cornerRadius: 24,
          interactiveDimiss: true,
          hPadding: 8,
          bPadding: 4
        )
      ) {
        SelectCoverSheet(
          viewModel: SelectCoverSheetVM(
            selectedType: $selectedType,
            isDarkBackground: $isDarkBackground,
            onDismiss: { showSheet = false }
          )
        )
      }
    }
  }
#endif
