<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg
PUBLIC "-//W3C//DTD SVG 1.1//EN"
     "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
     <!--Created with SF Symbol Generator (v1.0.0)-->

<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="3300" height="2200">
<!--glyph: "edit-big.medium", point size: 100.0-->
<style>.SFSymbolsPreviewWireframe {fill:none;opacity:1.0;stroke:black;stroke-width:0.5}
</style>
<g id="Notes">
<rect height="2200" id="artboard" style="fill:white;opacity:1" width="3300" x="0" y="0"/>
<line style="fill:none;stroke:black;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="292" y2="292"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 263 322)">Weight/Scale Variations</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 559.711 322)">Ultralight</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 856.422 322)">Thin</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1153.13 322)">Light</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1449.84 322)">Regular</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1746.56 322)">Medium</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2043.27 322)">Semibold</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2339.98 322)">Bold</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2636.69 322)">Heavy</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2933.4 322)">Black</text>
<line style="fill:none;stroke:black;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1903" y2="1903"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 263 1953)">Design Variations</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1971)">Symbols are supported in up to nine weights and three scales.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1989)">For optimal layout with text and other symbols, vertically align</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 2007)">symbols with the adjacent text.</text>
<line style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="776" x2="776" y1="1919" y2="1933"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 776 1953)">Margins</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 1971)">Leading and trailing margins on the left and right side of each symbol</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 1989)">can be adjusted by modifying the x-location of the margin guidelines.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 2007)">Modifications are automatically applied proportionally to all</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 2025)">scales and weights.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 1289 1953)">Exporting</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 1289 1971)">Symbols should be outlined when exporting to ensure the</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 1289 1989)">design is preserved when submitting to Xcode.</text>
<text id="template-version" style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1933)">Template v.5.0</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1951)">Requires Xcode 15 or greater</text>
<text id="descriptive-name" style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1969)">Generated from edit-big</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1987)">Typeset at 100.0 points</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 726)">Small</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1156)">Medium</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1586)">Large</text>
</g>
<g id="Guides">
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 696)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-S" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="696" y2="696"/>
<line id="Capline-S" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="625.541" y2="625.541"/>
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 1126)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-M" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1126" y2="1126"/>
<line id="Capline-M" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1055.54" y2="1055.54"/>
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 1556)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-L" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1556" y2="1556"/>
<line id="Capline-L" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1485.54" y2="1485.54"/>
<line id="left-margin-Ultralight-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="513.711" x2="513.711" y1="600.785" y2="720.121"/>
<line id="right-margin-Ultralight-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="605.711" x2="605.711" y1="600.785" y2="720.121"/>
<line id="left-margin-Regular-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="1403.84" x2="1403.84" y1="600.785" y2="720.121"/>
<line id="right-margin-Regular-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="1495.84" x2="1495.84" y1="600.785" y2="720.121"/>
<line id="left-margin-Black-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="2887.4" x2="2887.4" y1="600.785" y2="720.121"/>
<line id="right-margin-Black-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="2979.4" x2="2979.4" y1="600.785" y2="720.121"/>
</g>
<g id="Symbols">
<g id="Ultralight-S" transform="matrix(1 0 0 1 513.711 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="edit-big">
<g id="Union">
<path d="M43.125 11.4998C44.7127 11.4998 45.9999 12.7871 46 14.3748C46 15.9626 44.7128 17.2498 43.125 17.2498H32.7742C29.5069 17.2498 27.229 17.2509 25.4557 17.3958C23.7161 17.5379 22.7164 17.8037 21.9593 18.1894C20.3365 19.0163 19.0165 20.3363 18.1896 21.9591C17.8039 22.7162 17.5381 23.716 17.396 25.4555C17.2511 27.2288 17.25 29.5068 17.25 32.774V59.2255C17.25 62.4928 17.2511 64.7708 17.396 66.5441C17.5381 68.2834 17.8039 69.2832 18.1896 70.0405C19.0165 71.6633 20.3364 72.9833 21.9593 73.8102L22.5807 74.076C23.2628 74.3199 24.151 74.4972 25.4557 74.6038C27.229 74.7487 29.5069 74.7498 32.7742 74.7498H59.2257C62.4932 74.7498 64.771 74.7487 66.5443 74.6038C68.2837 74.4616 69.2834 74.1959 70.0407 73.8102C71.6635 72.9833 72.9836 71.6633 73.8104 70.0405C74.1961 69.2832 74.4619 68.2834 74.604 66.5441C74.7489 64.7708 74.75 62.4928 74.75 59.2255V48.8748C74.7501 47.2871 76.0373 45.9998 77.625 45.9998C79.2127 45.9998 80.4999 47.2871 80.5 48.8748V59.2255C80.5 62.3979 80.5037 64.9504 80.3353 67.012C80.1641 69.1073 79.7989 70.9469 78.9315 72.6497C77.5534 75.3546 75.3548 77.5531 72.6499 78.9313C70.9471 79.7988 69.1075 80.1639 67.0122 80.3351C64.9505 80.5035 62.3982 80.4998 59.2257 80.4998H32.7742C29.6019 80.4998 27.0495 80.5035 24.9878 80.3351C22.8925 80.1639 21.0528 79.7988 19.3501 78.9313C16.6453 77.5532 14.4467 75.3546 13.0685 72.6497C12.2011 70.9469 11.8359 69.1073 11.6647 67.012C11.4963 64.9504 11.5 62.3979 11.5 59.2255V32.774C11.5 29.6017 11.4963 27.0492 11.6647 24.9876C11.8359 22.8923 12.201 21.0526 13.0685 19.3499C14.4467 16.6451 16.6453 14.4465 19.3501 13.0683C21.0528 12.2008 22.8925 11.8357 24.9878 11.6645C27.0495 11.4961 29.6018 11.4998 32.7742 11.4998H43.125Z" fill="black"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M65.0506 11.3837C69.3488 7.08589 76.3179 7.08597 80.616 11.3837C84.9138 15.682 84.9138 22.6509 80.616 26.9492L47.0744 60.4908C46.5352 61.0298 45.804 61.3331 45.0417 61.3331H33.5417C31.9541 61.3331 30.6671 60.0456 30.6667 58.4581V46.9581C30.6669 46.1961 30.9702 45.4643 31.5089 44.9254L65.0506 11.3837ZM76.5506 15.4492C74.4979 13.3969 71.1687 13.3969 69.116 15.4492L36.4167 48.1485V55.5831H43.8512L76.5506 22.8837C78.6028 20.831 78.6029 17.502 76.5506 15.4492Z" fill="black"/>
</g>
</g>
</svg>

</g>
<g id="Regular-S" transform="matrix(1 0 0 1 1403.84 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="edit-big">
<g id="Union">
<path d="M43.125 11.4998C44.7127 11.4998 45.9999 12.7871 46 14.3748C46 15.9626 44.7128 17.2498 43.125 17.2498H32.7742C29.5069 17.2498 27.229 17.2509 25.4557 17.3958C23.7161 17.5379 22.7164 17.8037 21.9593 18.1894C20.3365 19.0163 19.0165 20.3363 18.1896 21.9591C17.8039 22.7162 17.5381 23.716 17.396 25.4555C17.2511 27.2288 17.25 29.5068 17.25 32.774V59.2255C17.25 62.4928 17.2511 64.7708 17.396 66.5441C17.5381 68.2834 17.8039 69.2832 18.1896 70.0405C19.0165 71.6633 20.3364 72.9833 21.9593 73.8102L22.5807 74.076C23.2628 74.3199 24.151 74.4972 25.4557 74.6038C27.229 74.7487 29.5069 74.7498 32.7742 74.7498H59.2257C62.4932 74.7498 64.771 74.7487 66.5443 74.6038C68.2837 74.4616 69.2834 74.1959 70.0407 73.8102C71.6635 72.9833 72.9836 71.6633 73.8104 70.0405C74.1961 69.2832 74.4619 68.2834 74.604 66.5441C74.7489 64.7708 74.75 62.4928 74.75 59.2255V48.8748C74.7501 47.2871 76.0373 45.9998 77.625 45.9998C79.2127 45.9998 80.4999 47.2871 80.5 48.8748V59.2255C80.5 62.3979 80.5037 64.9504 80.3353 67.012C80.1641 69.1073 79.7989 70.9469 78.9315 72.6497C77.5534 75.3546 75.3548 77.5531 72.6499 78.9313C70.9471 79.7988 69.1075 80.1639 67.0122 80.3351C64.9505 80.5035 62.3982 80.4998 59.2257 80.4998H32.7742C29.6019 80.4998 27.0495 80.5035 24.9878 80.3351C22.8925 80.1639 21.0528 79.7988 19.3501 78.9313C16.6453 77.5532 14.4467 75.3546 13.0685 72.6497C12.2011 70.9469 11.8359 69.1073 11.6647 67.012C11.4963 64.9504 11.5 62.3979 11.5 59.2255V32.774C11.5 29.6017 11.4963 27.0492 11.6647 24.9876C11.8359 22.8923 12.201 21.0526 13.0685 19.3499C14.4467 16.6451 16.6453 14.4465 19.3501 13.0683C21.0528 12.2008 22.8925 11.8357 24.9878 11.6645C27.0495 11.4961 29.6018 11.4998 32.7742 11.4998H43.125Z" fill="black"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M65.0506 11.3837C69.3488 7.08589 76.3179 7.08597 80.616 11.3837C84.9138 15.682 84.9138 22.6509 80.616 26.9492L47.0744 60.4908C46.5352 61.0298 45.804 61.3331 45.0417 61.3331H33.5417C31.9541 61.3331 30.6671 60.0456 30.6667 58.4581V46.9581C30.6669 46.1961 30.9702 45.4643 31.5089 44.9254L65.0506 11.3837ZM76.5506 15.4492C74.4979 13.3969 71.1687 13.3969 69.116 15.4492L36.4167 48.1485V55.5831H43.8512L76.5506 22.8837C78.6028 20.831 78.6029 17.502 76.5506 15.4492Z" fill="black"/>
</g>
</g>
</svg>

</g>
<g id="Black-S" transform="matrix(1 0 0 1 2887.4 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="edit-big">
<g id="Union">
<path d="M43.125 11.4998C44.7127 11.4998 45.9999 12.7871 46 14.3748C46 15.9626 44.7128 17.2498 43.125 17.2498H32.7742C29.5069 17.2498 27.229 17.2509 25.4557 17.3958C23.7161 17.5379 22.7164 17.8037 21.9593 18.1894C20.3365 19.0163 19.0165 20.3363 18.1896 21.9591C17.8039 22.7162 17.5381 23.716 17.396 25.4555C17.2511 27.2288 17.25 29.5068 17.25 32.774V59.2255C17.25 62.4928 17.2511 64.7708 17.396 66.5441C17.5381 68.2834 17.8039 69.2832 18.1896 70.0405C19.0165 71.6633 20.3364 72.9833 21.9593 73.8102L22.5807 74.076C23.2628 74.3199 24.151 74.4972 25.4557 74.6038C27.229 74.7487 29.5069 74.7498 32.7742 74.7498H59.2257C62.4932 74.7498 64.771 74.7487 66.5443 74.6038C68.2837 74.4616 69.2834 74.1959 70.0407 73.8102C71.6635 72.9833 72.9836 71.6633 73.8104 70.0405C74.1961 69.2832 74.4619 68.2834 74.604 66.5441C74.7489 64.7708 74.75 62.4928 74.75 59.2255V48.8748C74.7501 47.2871 76.0373 45.9998 77.625 45.9998C79.2127 45.9998 80.4999 47.2871 80.5 48.8748V59.2255C80.5 62.3979 80.5037 64.9504 80.3353 67.012C80.1641 69.1073 79.7989 70.9469 78.9315 72.6497C77.5534 75.3546 75.3548 77.5531 72.6499 78.9313C70.9471 79.7988 69.1075 80.1639 67.0122 80.3351C64.9505 80.5035 62.3982 80.4998 59.2257 80.4998H32.7742C29.6019 80.4998 27.0495 80.5035 24.9878 80.3351C22.8925 80.1639 21.0528 79.7988 19.3501 78.9313C16.6453 77.5532 14.4467 75.3546 13.0685 72.6497C12.2011 70.9469 11.8359 69.1073 11.6647 67.012C11.4963 64.9504 11.5 62.3979 11.5 59.2255V32.774C11.5 29.6017 11.4963 27.0492 11.6647 24.9876C11.8359 22.8923 12.201 21.0526 13.0685 19.3499C14.4467 16.6451 16.6453 14.4465 19.3501 13.0683C21.0528 12.2008 22.8925 11.8357 24.9878 11.6645C27.0495 11.4961 29.6018 11.4998 32.7742 11.4998H43.125Z" fill="black"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M65.0506 11.3837C69.3488 7.08589 76.3179 7.08597 80.616 11.3837C84.9138 15.682 84.9138 22.6509 80.616 26.9492L47.0744 60.4908C46.5352 61.0298 45.804 61.3331 45.0417 61.3331H33.5417C31.9541 61.3331 30.6671 60.0456 30.6667 58.4581V46.9581C30.6669 46.1961 30.9702 45.4643 31.5089 44.9254L65.0506 11.3837ZM76.5506 15.4492C74.4979 13.3969 71.1687 13.3969 69.116 15.4492L36.4167 48.1485V55.5831H43.8512L76.5506 22.8837C78.6028 20.831 78.6029 17.502 76.5506 15.4492Z" fill="black"/>
</g>
</g>
</svg>

</g>
</g>
</svg>