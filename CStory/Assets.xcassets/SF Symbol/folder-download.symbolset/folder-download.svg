<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg
PUBLIC "-//W3C//DTD SVG 1.1//EN"
     "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
     <!--Created with SF Symbol Generator (v1.0.0)-->

<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="3300" height="2200">
<!--glyph: "folder-download.medium", point size: 100.0-->
<style>.SFSymbolsPreviewWireframe {fill:none;opacity:1.0;stroke:black;stroke-width:0.5}
</style>
<g id="Notes">
<rect height="2200" id="artboard" style="fill:white;opacity:1" width="3300" x="0" y="0"/>
<line style="fill:none;stroke:black;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="292" y2="292"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 263 322)">Weight/Scale Variations</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 559.711 322)">Ultralight</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 856.422 322)">Thin</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1153.13 322)">Light</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1449.84 322)">Regular</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1746.56 322)">Medium</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2043.27 322)">Semibold</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2339.98 322)">Bold</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2636.69 322)">Heavy</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2933.4 322)">Black</text>
<line style="fill:none;stroke:black;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1903" y2="1903"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 263 1953)">Design Variations</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1971)">Symbols are supported in up to nine weights and three scales.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1989)">For optimal layout with text and other symbols, vertically align</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 2007)">symbols with the adjacent text.</text>
<line style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="776" x2="776" y1="1919" y2="1933"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 776 1953)">Margins</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 1971)">Leading and trailing margins on the left and right side of each symbol</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 1989)">can be adjusted by modifying the x-location of the margin guidelines.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 2007)">Modifications are automatically applied proportionally to all</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 2025)">scales and weights.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 1289 1953)">Exporting</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 1289 1971)">Symbols should be outlined when exporting to ensure the</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 1289 1989)">design is preserved when submitting to Xcode.</text>
<text id="template-version" style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1933)">Template v.5.0</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1951)">Requires Xcode 15 or greater</text>
<text id="descriptive-name" style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1969)">Generated from folder-download</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1987)">Typeset at 100.0 points</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 726)">Small</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1156)">Medium</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1586)">Large</text>
</g>
<g id="Guides">
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 696)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-S" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="696" y2="696"/>
<line id="Capline-S" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="625.541" y2="625.541"/>
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 1126)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-M" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1126" y2="1126"/>
<line id="Capline-M" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1055.54" y2="1055.54"/>
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 1556)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-L" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1556" y2="1556"/>
<line id="Capline-L" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1485.54" y2="1485.54"/>
<line id="left-margin-Ultralight-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="513.711" x2="513.711" y1="600.785" y2="720.121"/>
<line id="right-margin-Ultralight-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="605.711" x2="605.711" y1="600.785" y2="720.121"/>
<line id="left-margin-Regular-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="1403.84" x2="1403.84" y1="600.785" y2="720.121"/>
<line id="right-margin-Regular-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="1495.84" x2="1495.84" y1="600.785" y2="720.121"/>
<line id="left-margin-Black-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="2887.4" x2="2887.4" y1="600.785" y2="720.121"/>
<line id="right-margin-Black-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="2979.4" x2="2979.4" y1="600.785" y2="720.121"/>
</g>
<g id="Symbols">
<g id="Ultralight-S" transform="matrix(1 0 0 1 513.711 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="folder-download">
<g id="Union">
<path d="M32.179 11.5C36.9852 11.5001 41.4735 13.9023 44.1395 17.9014L46.116 20.8662L46.4754 21.3416C47.3789 22.3879 48.701 22.9999 50.1029 23H69.9583C77.8976 23 84.3333 29.436 84.3333 37.375V62.2917C84.3333 70.2309 77.8976 76.6667 69.9583 76.6667H63.7292C62.1413 76.6667 60.8542 75.3795 60.8542 73.7917C60.8542 72.2038 62.1413 70.9167 63.7292 70.9167H69.9583C74.722 70.9167 78.5833 67.0553 78.5833 62.2917V37.375C78.5833 32.6115 74.7219 28.75 69.9583 28.75H50.1029C46.5785 28.7499 43.2869 26.9884 41.3319 24.0557L39.3553 21.0908C37.7557 18.6914 35.0627 17.2501 32.179 17.25H22.0417C17.2782 17.25 13.4167 21.1116 13.4167 25.875V62.2917C13.4167 67.0553 17.2782 70.9167 22.0417 70.9167H28.2708C29.8587 70.9167 31.1458 72.2038 31.1458 73.7917C31.1458 75.3795 29.8587 76.6667 28.2708 76.6667H22.0417C14.1026 76.6667 7.66667 70.231 7.66667 62.2917V25.875C7.66667 17.9359 14.1026 11.5 22.0417 11.5H32.179Z" fill="black"/>
<path d="M46 42.1667C47.5878 42.1667 48.875 43.4538 48.875 45.0417V62.0596L53.5506 57.384C54.6734 56.2612 56.4933 56.2612 57.616 57.384C58.7388 58.5067 58.7388 60.3266 57.616 61.4494L48.0327 71.0327C46.91 72.1555 45.09 72.1555 43.9673 71.0327L34.384 61.4494C33.2612 60.3266 33.2612 58.5067 34.384 57.384C35.5067 56.2612 37.3266 56.2612 38.4494 57.384L43.125 62.0596V45.0417C43.125 43.4538 44.4122 42.1667 46 42.1667Z" fill="black"/>
</g>
</g>
</svg>

</g>
<g id="Regular-S" transform="matrix(1 0 0 1 1403.84 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="folder-download">
<g id="Union">
<path d="M32.179 11.5C36.9852 11.5001 41.4735 13.9023 44.1395 17.9014L46.116 20.8662L46.4754 21.3416C47.3789 22.3879 48.701 22.9999 50.1029 23H69.9583C77.8976 23 84.3333 29.436 84.3333 37.375V62.2917C84.3333 70.2309 77.8976 76.6667 69.9583 76.6667H63.7292C62.1413 76.6667 60.8542 75.3795 60.8542 73.7917C60.8542 72.2038 62.1413 70.9167 63.7292 70.9167H69.9583C74.722 70.9167 78.5833 67.0553 78.5833 62.2917V37.375C78.5833 32.6115 74.7219 28.75 69.9583 28.75H50.1029C46.5785 28.7499 43.2869 26.9884 41.3319 24.0557L39.3553 21.0908C37.7557 18.6914 35.0627 17.2501 32.179 17.25H22.0417C17.2782 17.25 13.4167 21.1116 13.4167 25.875V62.2917C13.4167 67.0553 17.2782 70.9167 22.0417 70.9167H28.2708C29.8587 70.9167 31.1458 72.2038 31.1458 73.7917C31.1458 75.3795 29.8587 76.6667 28.2708 76.6667H22.0417C14.1026 76.6667 7.66667 70.231 7.66667 62.2917V25.875C7.66667 17.9359 14.1026 11.5 22.0417 11.5H32.179Z" fill="black"/>
<path d="M46 42.1667C47.5878 42.1667 48.875 43.4538 48.875 45.0417V62.0596L53.5506 57.384C54.6734 56.2612 56.4933 56.2612 57.616 57.384C58.7388 58.5067 58.7388 60.3266 57.616 61.4494L48.0327 71.0327C46.91 72.1555 45.09 72.1555 43.9673 71.0327L34.384 61.4494C33.2612 60.3266 33.2612 58.5067 34.384 57.384C35.5067 56.2612 37.3266 56.2612 38.4494 57.384L43.125 62.0596V45.0417C43.125 43.4538 44.4122 42.1667 46 42.1667Z" fill="black"/>
</g>
</g>
</svg>

</g>
<g id="Black-S" transform="matrix(1 0 0 1 2887.4 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="folder-download">
<g id="Union">
<path d="M32.179 11.5C36.9852 11.5001 41.4735 13.9023 44.1395 17.9014L46.116 20.8662L46.4754 21.3416C47.3789 22.3879 48.701 22.9999 50.1029 23H69.9583C77.8976 23 84.3333 29.436 84.3333 37.375V62.2917C84.3333 70.2309 77.8976 76.6667 69.9583 76.6667H63.7292C62.1413 76.6667 60.8542 75.3795 60.8542 73.7917C60.8542 72.2038 62.1413 70.9167 63.7292 70.9167H69.9583C74.722 70.9167 78.5833 67.0553 78.5833 62.2917V37.375C78.5833 32.6115 74.7219 28.75 69.9583 28.75H50.1029C46.5785 28.7499 43.2869 26.9884 41.3319 24.0557L39.3553 21.0908C37.7557 18.6914 35.0627 17.2501 32.179 17.25H22.0417C17.2782 17.25 13.4167 21.1116 13.4167 25.875V62.2917C13.4167 67.0553 17.2782 70.9167 22.0417 70.9167H28.2708C29.8587 70.9167 31.1458 72.2038 31.1458 73.7917C31.1458 75.3795 29.8587 76.6667 28.2708 76.6667H22.0417C14.1026 76.6667 7.66667 70.231 7.66667 62.2917V25.875C7.66667 17.9359 14.1026 11.5 22.0417 11.5H32.179Z" fill="black"/>
<path d="M46 42.1667C47.5878 42.1667 48.875 43.4538 48.875 45.0417V62.0596L53.5506 57.384C54.6734 56.2612 56.4933 56.2612 57.616 57.384C58.7388 58.5067 58.7388 60.3266 57.616 61.4494L48.0327 71.0327C46.91 72.1555 45.09 72.1555 43.9673 71.0327L34.384 61.4494C33.2612 60.3266 33.2612 58.5067 34.384 57.384C35.5067 56.2612 37.3266 56.2612 38.4494 57.384L43.125 62.0596V45.0417C43.125 43.4538 44.4122 42.1667 46 42.1667Z" fill="black"/>
</g>
</g>
</svg>

</g>
</g>
</svg>