//
//  SelectCurrency.swift
//  CStory
//
//  Created by nzue on 2025/8/20.
//

import SwiftUI

/// 选择货币弹窗组件
/// 专门用于货币选择的 Sheet 组件，接收一个货币代码并返回选中的货币代码
struct SelectCurrency: View {

  // MARK: - Properties

  /// 当前选中的货币ID（外部传入）
  @State private var selectedCurrencyId: UUID?

  /// 数据管理器
  @Environment(\.dataManager) private var dataManager

  /// 关闭弹窗
  @Environment(\.dismiss) private var dismiss

  /// 选择完成回调
  let onCurrencySelected: (CurrencyModel?) -> Void

  // MARK: - Initialization

  /// 初始化 SelectCurrency
  /// - Parameters:
  ///   - selectedCurrencyId: 当前选中的货币ID
  ///   - onCurrencySelected: 选择完成回调，返回完整的货币信息
  init(
    selectedCurrencyId: UUID? = nil,
    onCurrencySelected: @escaping (CurrencyModel?) -> Void
  ) {
    self._selectedCurrencyId = State(initialValue: selectedCurrencyId)
    self.onCurrencySelected = onCurrencySelected
  }

  // MARK: - Body

  var body: some View {
    VStack(spacing: 12.0) {
      // 标题栏
      HStack {
        Text("选择货币")
          .font(.system(size: 16, weight: .medium))
          .foregroundColor(.cBlack)
        Spacer()
        // 关闭按钮
        Image("circle-x")
          .foregroundColor(.cBlack.opacity(0.2))
          .font(.system(size: 18, weight: .medium))
          .onTapGesture {
            dismiss()
          }
      }
      .padding(.top, 12)
      .padding(.horizontal, 16)
      // 货币列表
      ScrollView {
        LazyVStack(spacing: 12) {
          // 循环显示货币
          ForEach(dataManager.currencies, id: \.id) { currency in
            Row(
              leftMainText: currency.name,
              rightMainText: "\(currency.code) · \(currency.symbol)",
              selectBorder: currency.id == selectedCurrencyId,
              onRowTap: {
                selectedCurrencyId = currency.id
                dataManager.hapticManager.trigger(.selection)
                onCurrencySelected(currency)  // 返回完整的货币信息
                dismiss()
              }
            )
          }
        }
        .padding(.vertical, 12)
        .padding(.horizontal, 16)
      }
    }

  }
}

#Preview {
  // 创建预览用的货币数据
  let sampleCurrencies = [
    CurrencyModel(
      name: "人民币",
      code: "CNY",
      symbol: "¥",
      rate: 1.0,
      isBaseCurrency: true,
      order: 0
    ),
    CurrencyModel(
      name: "美元",
      code: "USD",
      symbol: "$",
      rate: 7.2,
      isBaseCurrency: false,
      order: 1
    ),
    CurrencyModel(
      name: "欧元",
      code: "EUR",
      symbol: "€",
      rate: 7.8,
      isBaseCurrency: false,
      order: 2
    ),
    CurrencyModel(
      name: "日元",
      code: "JPY",
      symbol: "¥",
      rate: 0.05,
      isBaseCurrency: false,
      order: 3
    ),
  ]

  // 创建预览用的数据管理器
  let previewDataManager = DataManagement(currencies: sampleCurrencies)

  VStack(spacing: 12.0) {
    Spacer()
    SelectCurrency(
      selectedCurrencyId: nil,  // 预览中不设置选中状态
      onCurrencySelected: { currency in
        print("选择了货币: \(currency?.name ?? "无") (\(currency?.code ?? "无"))")
      }
    )
    Spacer()
  }
  .background(.cBeige)
  .withDataManager(previewDataManager)
}
