//
//  AIPromptService.swift
//  CStory
//
//  Created by NZUE on 2025/07/03.
//

import Foundation
import SwiftData

/// AI 提示词服务
///
/// 负责为 AI 助手生成包含交易类别和卡片信息的完整系统提示词。
/// 使用扁平化的 JSON 结构，简化数据传输和处理。
class AIPromptService {
  /// 共享实例
  static let shared = AIPromptService()

  /// 私有构造方法，确保单例模式
  private init() {}

  // MARK: - 公共接口

  /// 生成完整的 AI 系统提示词
  ///
  /// 包含基础提示词、交易类别数据和卡片数据。
  ///
  /// - Parameter modelContext: SwiftData 模型上下文，用于数据库查询
  /// - Returns: 完整的系统提示词字符串
  func generateSystemPrompt(modelContext: ModelContext) -> String {
    let currentDate = DateFormatter.localizedString(
      from: Date(), dateStyle: .medium, timeStyle: .none)
    let currentTimestamp = Date().timeIntervalSince1970

    return """
      你是CStory智能记账助手，帮助用户记录财务交易。

      当前时间：\(currentDate)
      当前时间戳：\(Int(currentTimestamp))

      请从用户输入或图片中提取交易信息，返回 transactions 数组格式。
      每个交易包含以下字段：
      1. transactionType: expense(支出)/income(收入)/transfer(转账)/refund(退款)/installment(分期)
      2. timestamp: 交易时间戳（Unix时间戳，秒级），默认当前时间戳
      3. categoryId: 从下面类别列表中选择最合适的ID
      4. cardId: 从下面卡片列表中选择ID，可为空字符串
      5. amount: 交易金额，正数
      6. currency: 交易货币代码，如果用户未指定则使用卡片货币，如果卡片也未指定则使用"CNY"
      7. note: 简短备注
      8. discount: 优惠金额（仅支出类型需要），正数，默认0

      重要规则：
      - 如果用户输入中没有明确的交易信息（如金额、商品、服务等），请返回空的transactions数组：{"transactions": []}
      - 只有在能够明确识别出交易金额和交易内容时，才创建交易记录
      - 对于问候语、闲聊、询问等非交易内容，返回空数组

      时间戳说明：
      - 当前时间戳：\(Int(currentTimestamp))
      - 如果识别到图片里存在时间信息 转换成片时间戳并使用
      - 如果用户说"今天"、"刚才"等，使用当前时间戳
      - 如果用户说"昨天"，使用当前时间戳减去86400（一天的秒数）
      - 如果用户说"上午10点"，计算今天上午10点的时间戳
      - 时间戳必须是整数（秒级精度）

      支持多笔交易：
      - 单笔："今天买菜花了50元" → 返回1个交易
      - 多笔："买菜50元，打车20元" → 返回2个交易
      - 如果没有明确时间差异，多笔交易使用相同时间戳

      可用的交易类别（请根据用户输入选择最合适的类别ID）：
      格式：[[id, name, type], ...] 其中type为expense/income
      \(getCategoriesJSON(modelContext: modelContext))

      可用的资产账户（请根据用户输入选择最合适的卡片ID）：
      格式：[[id, name, currency, type], ...] 其中type为credit/saving
      \(getCardsJSON(modelContext: modelContext))
      """
  }

  // MARK: - 数据获取方法

  /// 获取所有交易类别数据（压缩格式）
  ///
  /// 将主类别和子类别合并为一个扁平的数组，使用数组格式节省 token。
  ///
  /// - Parameter modelContext: SwiftData 模型上下文
  /// - Returns: 交易类别的 JSON 字符串，格式：[[id, name, type], ...]
  func getCategoriesJSON(modelContext: ModelContext) -> String {
    do {
      // 获取所有主类别（按排序顺序）
      let mainCategoriesDescriptor = FetchDescriptor<TransactionMainCategoryModel>(
        sortBy: [SortDescriptor(\TransactionMainCategoryModel.order, order: .forward)]
      )
      let allMainCategories = try modelContext.fetch(mainCategoriesDescriptor)

      // 获取所有子类别（按排序顺序）
      let subCategoriesDescriptor = FetchDescriptor<TransactionSubCategoryModel>(
        sortBy: [SortDescriptor(\TransactionSubCategoryModel.order, order: .forward)]
      )
      let allSubCategories = try modelContext.fetch(subCategoriesDescriptor)

      // 构建压缩的类别数据 [id, name, type]
      var allCategories: [[String]] = []

      // 添加主类别数据
      for mainCategory in allMainCategories {
        allCategories.append([
          mainCategory.id,
          mainCategory.name,
          mainCategory.type,
        ])
      }

      // 添加子类别数据（继承主类别的 type）
      for subCategory in allSubCategories {
        let type = allMainCategories.first { $0.id == subCategory.mainId }?.type ?? "expense"
        allCategories.append([
          subCategory.id,
          subCategory.name,
          type,
        ])
      }

      // 序列化为 JSON（压缩格式，节省 token）
      let jsonData = try JSONSerialization.data(
        withJSONObject: allCategories, options: [])
      return String(data: jsonData, encoding: .utf8) ?? "[]"

    } catch {
      print("❌ 获取交易类别失败: \(error)")
      return "[]"
    }
  }

  /// 获取所有可用卡片数据（压缩格式）
  ///
  /// 只返回用户已选择的卡片（isSelected = true），使用数组格式节省 token。
  ///
  /// - Parameter modelContext: SwiftData 模型上下文
  /// - Returns: 卡片数据的 JSON 字符串，格式：[[id, name, currency, type], ...]
  /// - Note: 只包含 isSelected 为 true 的卡片
  func getCardsJSON(modelContext: ModelContext) -> String {
    do {
      // 获取所有已选择的卡片（按排序顺序）
      let cardsDescriptor = FetchDescriptor<CardModel>(
        predicate: #Predicate<CardModel> { $0.isSelected == true },
        sortBy: [SortDescriptor(\CardModel.order, order: .forward)]
      )
      let selectedCards = try modelContext.fetch(cardsDescriptor)

      // 构建压缩的卡片数据 [id, name, currency, type]
      var allCards: [[String]] = []

      for card in selectedCards {
        allCards.append([
          card.id.uuidString,
          card.name,
          card.currency,
          card.isCredit ? "credit" : "saving",
        ])
      }

      // 序列化为 JSON（压缩格式，节省 token）
      let jsonData = try JSONSerialization.data(
        withJSONObject: allCards, options: [])
      return String(data: jsonData, encoding: .utf8) ?? "[]"

    } catch {
      print("❌ 获取卡片数据失败: \(error)")
      return "[]"
    }
  }

}
