import SwiftUI

/// 自定义搜索栏组件
///
/// 专为配合 NavigationBarKit 设计的搜索栏
struct SearchBarKit: View {

  // MARK: - Properties

  @Binding var searchText: String
  @State private var isExpanded: Bool = true
  @State private var isEditing: Bool = false

  let placeholder: String
  let onSearchTextChanged: ((String) -> Void)?

  // MARK: - Initialization

  init(
    searchText: Binding<String>,
    placeholder: String = "搜索",
    onSearchTextChanged: ((String) -> Void)? = nil
  ) {
    self._searchText = searchText
    self.placeholder = placeholder
    self.onSearchTextChanged = onSearchTextChanged
  }

  // MARK: - Body

  var body: some View {
    VStack(spacing: 0) {
      if isExpanded {
        searchBar
          .transition(.move(edge: .top).combined(with: .opacity))
      }
    }
    .animation(.easeInOut(duration: 0.3), value: isExpanded)

  }

  // MARK: - Subviews

  private var searchBar: some View {
    HStack(spacing: 8) {
      // 搜索图标
      Image("magnifying-glass")
        .foregroundColor(.gray)
        .font(.system(size: 16))

      // 搜索文本框
      TextField(placeholder, text: $searchText)
        .textFieldStyle(PlainTextFieldStyle())
        .onTapGesture {
          isEditing = true
        }
        .onChange(of: searchText) { _, newValue in
          onSearchTextChanged?(newValue)
        }

      // 清除按钮
      if !searchText.isEmpty {
        Button(action: {
          searchText = ""
          onSearchTextChanged?("")
        }) {
          Image("circle-x")
            .foregroundColor(.gray)
            .font(.system(size: 16))
        }
        .transition(.scale.combined(with: .opacity))
      }

      // 取消按钮（编辑时显示）
      if isEditing {
        Button("取消") {
          withAnimation(.easeInOut(duration: 0.3)) {
            isEditing = false
            isExpanded = false
            searchText = ""
            onSearchTextChanged?("")
            // 取消键盘焦点
            UIApplication.shared.sendAction(
              #selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
          }
        }
        .foregroundColor(.blue)
        .transition(.move(edge: .trailing).combined(with: .opacity))
      }
    }
    .padding(.horizontal, 16)
    .padding(.vertical, 12)
    .background(Color(.systemGray6))
    .cornerRadius(10)
    .padding(.horizontal, 16)
    .padding(.vertical, 8)
  }
}

// MARK: - Preview

#Preview {
  VStack {
    NavigationBarKit(
      title: "测试页面",
      onBack: {
        print("返回")
      }
    )

    SearchBarKit(
      searchText: .constant(""),
      placeholder: "搜索货币名称或代码"
    )

    ScrollView {
      LazyVStack {
        ForEach(0..<20) { index in
          HStack {
            Text("项目 \(index)")
            Spacer()
          }
          .padding()
          .background(Color.white)
          .cornerRadius(8)
          .padding(.horizontal)
        }
      }
      .padding(.top)
    }
  }
  .background(.cLightBlue)
}
