//
//  CurrencyModel.swift
//  CStory
//
//  Created by NZUE on 2025/6/11.
//

import Foundation
import SwiftData

// MARK: - 货币模型

/// 货币数据模型
///
/// 管理应用中的货币信息和汇率转换。
/// - Note: 只能有一个货币设置为本位币
@Model
class CurrencyModel: Identifiable {
  /// 唯一标识符
  var id: UUID = UUID()
  /// 货币名称（如："美元"、"欧元"）
  var name: String = ""
  /// 货币代码（如："USD"、"EUR"）
  var code: String = ""
  /// 货币符号（如："$"、"€"）
  var symbol: String = ""
  /// 汇率：1单位外币等于多少本位币
  /// - Note: 如美元对人民币约7.25，表示1美元=7.25人民币
  var rate: Double = 0.0
  /// 自定义汇率（用户修改过的汇率）
  var customRate: Double?
  /// 默认汇率（从API获取的原始汇率）
  var defaultRate: Double = 0.0
  /// 是否为本位币
  var isBaseCurrency: Bool = false
  /// 是否为自定义汇率
  var isCustom: Bool = false
  /// 是否被选中使用
  var isSelected: Bool = true
  /// 排序顺序（数字越小越靠前）
  var order: Int = 999
  /// 创建日期
  var createdAt: Date = Date()
  /// 更新日期
  var updatedAt: Date = Date()

  init(
    id: UUID = UUID(),
    name: String = "",
    code: String = "",
    symbol: String = "",
    rate: Double = 0.0,
    customRate: Double? = nil,
    defaultRate: Double = 0.0,
    isBaseCurrency: Bool = false,
    isCustom: Bool = false,
    isSelected: Bool = true,
    order: Int = 999,  // 默认排序靠后
    createdAt: Date = Date(),
    updatedAt: Date = Date()
  ) {
    self.id = id
    self.name = name
    self.code = code
    self.symbol = symbol
    self.rate = rate
    self.customRate = customRate
    self.defaultRate = defaultRate
    self.isBaseCurrency = isBaseCurrency
    self.isCustom = isCustom
    self.isSelected = isSelected
    self.order = order
    self.createdAt = createdAt
    self.updatedAt = updatedAt
  }

  // MARK: - 方法

  /// 恢复默认汇率
  func restoreDefaultRate() {
    if defaultRate > 0 {
      self.rate = defaultRate
      self.customRate = nil
      self.isCustom = false
      self.updatedAt = Date()
    }
  }

  /// 格式化汇率显示
  ///
  /// 显示最多6位小数，并移除多余的零。
  /// - Returns: 格式化后的汇率字符串
  func formattedRate() -> String {
    // 先格式化为6位小数
    let formatted = String(format: "%.6f", rate)
    // 去除尾随的0
    var result = formatted
    while result.hasSuffix("0") {
      result.removeLast()
    }
    // 如果最后以.结尾，也把.去掉
    if result.hasSuffix(".") {
      result.removeLast()
    }
    return result
  }
}

// MARK: - 本位币管理

extension CurrencyModel {
  /// 设置此货币为本位币
  ///
  /// 会重新计算所有其他货币的汇率。
  /// - Parameter context: 模型上下文
  /// - Throws: 当保存失败时抛出错误
  func setAsBaseCurrency(in context: ModelContext) throws {
    // 1. 查找当前的本位币
    let descriptor = FetchDescriptor<CurrencyModel>(
      predicate: #Predicate { $0.isBaseCurrency == true })
    let currentBaseCurrencies = try context.fetch(descriptor)

    // 确保我们使用的是这个货币的默认汇率（如果有的话），否则使用当前汇率
    let newBaseCurrencyRate = self.defaultRate > 0 ? self.defaultRate : self.rate

    // 2. 将当前所有本位币设置为非本位币
    for currency in currentBaseCurrencies {
      currency.isBaseCurrency = false
    }

    // 3. 设置新的本位币
    self.isBaseCurrency = true
    self.rate = 1.0  // 本位币的汇率始终为1.0
    self.defaultRate = 1.0  // 默认汇率也设为1.0
    self.customRate = nil  // 清除自定义汇率
    self.isCustom = false  // 重置自定义标记

    // 更新 UserDefaults 中的本位币设置
    UserDefaults.standard.set(self.code, forKey: "baseCurrencyCode")

    // 4. 获取所有货币
    let allCurrenciesDescriptor = FetchDescriptor<CurrencyModel>()
    let allCurrencies = try context.fetch(allCurrenciesDescriptor)

    // 5. 重新计算所有货币相对于新本位币的汇率
    for currency in allCurrencies where currency.id != self.id {
      if !currency.isBaseCurrency {
        // 使用货币的默认汇率（如果有）进行计算，否则使用当前汇率
        let currencyRate = currency.defaultRate > 0 ? currency.defaultRate : currency.rate

        // 新汇率 = 旧汇率 × (1/新本位币旧汇率)
        // 例如: 人民币变美元为本位币：原美元汇率7.25变为1.0，人民币变为0.138
        if newBaseCurrencyRate > 0 {
          let newRate = currencyRate / newBaseCurrencyRate
          let roundedRate = (newRate * 1_000_000).rounded() / 1_000_000

          currency.rate = roundedRate
          currency.defaultRate = roundedRate  // 默认汇率也更新
          currency.customRate = nil  // 清除所有自定义汇率
          currency.isCustom = false  // 重置所有自定义标记
        }
      }
    }

    // 6. 保存更改
    try context.save()
  }
}

// MARK: - API 响应模型

/// 汇率API响应模型
struct ExchangeRateAPIResponse: Codable {
  /// 汇率数据
  struct ExchangeRateData: Codable {
    /// 基础货币
    var base: String
    /// 最后更新时间
    var last_updated: String
    /// 汇率列表
    var rates: [String: Double]
  }

  /// 数据字段
  var data: ExchangeRateData
  /// 状态字段
  var status: String
}
