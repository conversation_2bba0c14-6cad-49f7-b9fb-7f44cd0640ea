//
//  TransactionRecordVM.swift
//  CStory
//
//  Created by 咩咩 on 2025/7/18.
//

import Combine
import SwiftUI

/// 交易记录视图模型
///
/// 核心逻辑：
/// 1. 从DataManagement获取所有交易
/// 2. 根据时间周期（年/月/周）和具体时间筛选交易
/// 3. 排除创建卡片和调整卡片类型的交易
/// 4. 计算收入和支出统计
/// 5. 按日期分组生成UI数据
///
/// 时间控制：
/// - 默认显示当月数据
/// - 支持年/月/周切换
/// - 支持上一个/下一个导航
/// - 时间变化时自动重新处理数据
@MainActor
final class TransactionRecordVM: ObservableObject {

  // MARK: - 数据源

  /// 数据管理器
  private let dataManager: DataManagement

  /// 交易点击回调
  private let onTransactionTap: ((TransactionModel) -> Void)?

  // MARK: - UI数据

  /// 收入支出统计数据
  ///
  /// 预先准备好的收入支出统计数据，包含收入、支出金额和货币符号。
  /// View层可以直接使用，无需在UI层组装数据。
  @Published var incomeExpenseData: (income: Double, expense: Double, currencySymbol: String)

  /// 时间控制器 - 管理时间选择逻辑
  @Published var timeControlVM: TimeControlVM

  /// 按日期分组的交易数据（包含完整的TransactionRowVM）
  @Published var transactionDayGroups: [TransactionDayGroupWithRowVM] = []

  // MARK: - 初始化

  init(dataManager: DataManagement, onTransactionTap: ((TransactionModel) -> Void)? = nil) {
    self.dataManager = dataManager
    self.onTransactionTap = onTransactionTap

    // 初始化收入支出统计数据
    let baseCurrencyCode = UserDefaults.standard.string(forKey: "baseCurrencyCode") ?? "CNY"
    let currencySymbol = dataManager.currencies.first { $0.code == baseCurrencyCode }?.symbol ?? "¥"
    self.incomeExpenseData = (
      income: 0.0,
      expense: 0.0,
      currencySymbol: currencySymbol
    )

    // 初始化时间控制器 - 先不设置回调
    self.timeControlVM = TimeControlVM(selectedPeriod: .month, currentDate: Date()) { _, _ in }

    // 设置时间控制器的回调
    self.timeControlVM.onDateChange = { [weak self] date, period in
      self?.processData()
    }

    // 处理初始数据（当月）
    processData()
  }

  // MARK: - 公共方法

  /// 当前选择的日期（从时间控制器获取）
  var currentDate: Date {
    return timeControlVM.currentDate
  }

  /// 当前选择的时间周期（从时间控制器获取）
  var selectedPeriod: TransactionTimePeriod {
    return timeControlVM.selectedPeriod
  }

  // MARK: - 核心数据处理

  /// 处理数据的核心方法
  /// 使用新的综合服务一次性获取交易列表数据和收支统计
  private func processData() {
    // 1. 获取时间范围
    let dateRange = getDateRange()

    // 2. 使用综合服务一次性获取所有数据
    let result = TransactionListDataService.shared.getTransactionListWithStatistics(
      dataManager: dataManager,
      dateRange: dateRange,
      onTransactionTap: { [weak self] transaction in
        // 移除重复的震动反馈，让业务层统一处理
        self?.onTransactionTap?(transaction)
      }
    )

    // 3. 更新UI数据
    transactionDayGroups = result.transactionDayGroups
    incomeExpenseData = (
      income: result.incomeExpenseStatistics.totalIncome,
      expense: result.incomeExpenseStatistics.totalExpense,
      currencySymbol: result.incomeExpenseStatistics.currencySymbol
    )
  }

  // MARK: - 辅助方法

  /// 获取时间范围（使用时间控制器的计算结果）
  private func getDateRange() -> (start: Date, end: Date) {
    return timeControlVM.dateRange
  }

}
