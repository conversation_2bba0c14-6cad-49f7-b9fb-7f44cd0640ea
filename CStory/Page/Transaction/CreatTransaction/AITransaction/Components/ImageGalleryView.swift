//
//  ImageGalleryView.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//

import SwiftUI

/// 图片列表视图
///
/// 在AI聊天界面显示用户选中的图片列表，支持查看、删除和添加更多图片。
/// 以水平滚动列表形式展示，每张图片都有删除按钮。
///
/// ## 功能特性
/// - 水平滚动浏览
/// - 点击删除单张图片
/// - 添加更多图片按钮
/// - 触觉反馈
struct ImageGalleryView: View {
  /// 选中的图片数组
  @Binding var selectedImages: [UIImage]
  /// 控制图片选择器显示
  @Binding var showImagePicker: Bool
  /// 数据管理器
  @Environment(\.dataManager) private var dataManager

  var body: some View {
    if !selectedImages.isEmpty {
      ScrollView(.horizontal, showsIndicators: false) {
        HStack(spacing: 8) {
          // 显示每张选中的图片
          ForEach(selectedImages.indices, id: \.self) { index in
            ZStack(alignment: .topTrailing) {
              // 图片缩略图
              Image(uiImage: selectedImages[index])
                .resizable()
                .scaledToFill()
                .frame(width: 80, height: 80)
                .clipShape(RoundedRectangle(cornerRadius: 12))
                .overlay(
                  RoundedRectangle(cornerRadius: 12)
                    .strokeBorder(.cBlack.opacity(0.3), lineWidth: 1)
                )
                .transition(
                  .asymmetric(
                    insertion: .scale.combined(with: .opacity),
                    removal: .scale.combined(with: .opacity)
                  ))

              // 删除按钮
              Button(action: {
                dataManager.hapticManager.trigger(.impactLight)
                let _ = withAnimation(.easeInOut(duration: 0.3)) {
                  selectedImages.remove(at: index)
                }
              }) {
                Image("circle-x")
                  .foregroundColor(.cBlack.opacity(0.6))
                  .background(.cWhite.opacity(0.5))
                  .clipShape(Circle())
              }
              .padding(4)
            }
          }

          // 添加更多图片按钮
          Button(action: {
            dataManager.hapticManager.trigger(.selection)
            showImagePicker = true
          }) {
            Image("plus-large")
              .foregroundColor(.cBlack.opacity(0.4))
              .font(.system(size: 22, weight: .regular))
              .frame(width: 80, height: 80)
              .background(.cWhite.opacity(0.5))
              .cornerRadius(12)
              .overlay(
                RoundedRectangle(cornerRadius: 12)
                  .strokeBorder(.cBlack.opacity(0.3), lineWidth: 1)
              )
          }
        }
        .padding(.horizontal, 12)
      }
      // 当图片超过3张时，默认滚动到末尾
      .defaultScrollAnchor(selectedImages.count > 3 ? .trailing : .leading)
      .frame(height: selectedImages.isEmpty ? 0 : 80)
      .padding(.top, 12)
    }
  }
}

#Preview {
  @Previewable @State var images: [UIImage] = []
  @Previewable @State var showPicker = false

  return ImageGalleryView(
    selectedImages: $images,
    showImagePicker: $showPicker
  )
}
