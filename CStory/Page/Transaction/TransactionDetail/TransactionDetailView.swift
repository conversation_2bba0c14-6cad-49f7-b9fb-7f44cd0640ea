//
//  TransactionDetailView.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//

import SwiftData
import SwiftUI

/// 交易详情视图
///
/// 显示交易的详细信息，包括金额、分类、时间、备注等。
/// 支持编辑模式，可修改交易信息并保存。
///
/// ## 功能特性
/// - 查看交易详细信息
/// - 编辑交易各项属性
/// - 删除交易记录
/// - 退款操作支持
/// - 实时汇率转换显示
struct TransactionDetailView: View {
  // MARK: - 属性

  let transaction: TransactionModel

  // MARK: - 环境属性

  @Environment(\.modelContext) private var modelContext
  @Environment(\.dismiss) private var dismiss
  @Environment(\.dataManager) private var dataManager
  @EnvironmentObject private var pathManager: PathManagerHelper

  // MARK: - 状态属性

  // 使用 StateObject 来管理 ViewModel，避免视图重建时重复创建
  @StateObject private var viewModel: TransactionDetailVM

  init(transaction: TransactionModel, dataManager: DataManagement) {
    self.transaction = transaction
    self._viewModel = StateObject(
      wrappedValue: TransactionDetailVM(
        transaction: transaction,
        dataManager: dataManager
      ))
  }

  /// 顶部区域视图
  ///
  /// 包含交易标题、时间、状态标签和备注信息
  private var topArea: some View {
    VStack(spacing: 8) {
      /// 交易标题
      HStack(spacing: 12) {
        Text(
          viewModel.detailData?.transactionTitle
            ?? (transaction.remark.isEmpty ? "交易记录" : transaction.remark)
        )
        .font(.system(size: 20, weight: .medium))
        .foregroundColor(.cBlack)
        .frame(maxWidth: .infinity, alignment: .leading)

        if let data = viewModel.detailData {
          if data.hasRefund {
            HStack(spacing: 4) {
              Circle()
                .frame(width: 6, height: 6)
                .foregroundColor(.cAccentGreen)
              Text("退款")
                .font(.system(size: 13))
                .foregroundColor(.cBlack.opacity(0.6))
            }
          }

          if data.hasDiscount {
            HStack(spacing: 4) {
              Circle()
                .frame(width: 6, height: 6)
                .foregroundColor(.orange)
              Text("优惠")
                .font(.system(size: 13))
                .foregroundColor(.cBlack.opacity(0.6))
            }
          }
        }
      }
      /// 交易时间
      HStack(spacing: 12) {
        Text(
          getCurrentDisplayData()?.formattedDate
            ?? DateFormattingHelper.shared.smartFormat(date: transaction.transactionDate)
        )
        .font(.system(size: 15))
        .foregroundColor(.cBlack.opacity(0.6))

        if viewModel.isEditing {
          Button(action: {
            dataManager.hapticManager.trigger(.selection)
            viewModel.showTimeSheet = true
          }) {
            Image("highlight")
              .font(.system(size: 16))
              .foregroundColor(.cAccentBlue)
          }
          .transition(.move(edge: .trailing).combined(with: .opacity))
        }

        Spacer()
      }

      // 备注显示区域
      if viewModel.isEditing || !transaction.remark.isEmpty {
        HStack(alignment: .center, spacing: 8) {
          Text("备注：")
            .font(.system(size: 15))
            .foregroundColor(.cBlack.opacity(0.6))

          if viewModel.isEditing {
            TextField(
              "添加备注",
              text: Binding(
                get: { viewModel.editingData?.note ?? transaction.remark },
                set: { viewModel.editingData?.note = $0 }
              )
            )
            .font(.system(size: 15))
            .padding(8)
            .background(.cAccentBlue.opacity(0.05))
            .cornerRadius(8)
          } else {
            Text(transaction.remark)
              .font(.system(size: 15))
              .foregroundColor(.cBlack.opacity(0.6))
          }
          Spacer()
        }

      }
    }
    .padding(.vertical, 24)
    .padding(.horizontal, 16)
  }

  ///交易明细
  private var transactionDetail: some View {
    VStack(spacing: 12) {
      if let data = getCurrentDisplayData() {
        // 根据交易类型显示不同的编辑选项
        if transaction.transactionType == .refund {
          // 退款交易：可以编辑退款金额
          TransactionAmountRowView(
            title: "退款金额",
            currencySymbol: data.currencySymbol,
            amount: data.subtotal,
            isTotal: false,
            isEditing: viewModel.isEditing,
            onEditTap: {
              dataManager.hapticManager.trigger(.selection)
              viewModel.openNumericKeypad(for: .amount)
            }
          )
        } else {
          // 原始交易：正常的小计、优惠、退款、合计显示
          TransactionAmountRowView(
            title: data.hasDiscount || data.hasRefund ? "小计" : "总计",
            currencySymbol: data.currencySymbol,
            amount: data.subtotal,
            isTotal: !(data.hasDiscount || data.hasRefund),
            isEditing: viewModel.isEditing,
            onEditTap: {
              dataManager.hapticManager.trigger(.selection)
              viewModel.openNumericKeypad(for: .amount)
            }
          )

          if data.discountAmount > 0
            || (viewModel.isEditing
              && transaction.transactionType == .expense)
          {
            TransactionAmountRowView(
              title: "优惠",
              currencySymbol: data.currencySymbol,
              amount: data.discountAmount,
              isTotal: false,
              isEditing: viewModel.isEditing,
              onEditTap: {
                dataManager.hapticManager.trigger(.selection)
                viewModel.openNumericKeypad(for: .discount)
              }
            )
          }

          // 显示汇总的退款金额
          if data.refundAmount > 0 {
            TransactionAmountRowView(
              title: "已退款",
              currencySymbol: data.currencySymbol,
              amount: data.refundAmount,
              isTotal: false,
              isEditing: false,  // 退款金额不可编辑，应通过退款交易管理
              onEditTap: nil
            )
          }

          // 只有在有优惠或退款时才显示合计
          if data.hasDiscount || data.hasRefund {
            TransactionAmountRowView(
              title: "合计",
              currencySymbol: data.currencySymbol,
              amount: data.totalAmount,
              isTotal: true,
              isEditing: false,  // 合计不可编辑
              onEditTap: nil
            )
          }
        }
      } else {
        // 使用交易原始数据作为fallback，而不是显示加载状态
        TransactionAmountRowView(
          title: "总计",
          currencySymbol: transaction.symbol,
          amount: transaction.transactionAmount,
          isTotal: true,
          isEditing: false,
          onEditTap: nil
        )
      }
    }
    .padding(.top, 12)
    .padding(.horizontal, 24)
  }

  /// 操作按钮区域
  private var actionButtonSection: some View {
    VStack {
      Spacer()
      ZStack {
        VariableBlurView(maxBlurRadius: 15, direction: .blurredBottomClearTop, startOffset: -0.05)
          .ignoresSafeArea()
          .frame(height: 64)

        HStack(spacing: 32) {
          Spacer()

          if viewModel.isEditing {
            // 编辑模式按钮组：取消 + 保存
            HStack(spacing: 32) {
              Button(action: handleCancelAction) {
                Text("取消")
                  .font(.system(size: 16, weight: .medium))
                  .foregroundColor(.cBlack.opacity(0.6))
                  .padding(.horizontal, 36)
                  .frame(height: 40)
                  .background(.cBlack.opacity(0.08))
                  .cornerRadius(20)
              }

              Button(action: handleSaveAction) {
                Text("保存")
                  .font(.system(size: 16, weight: .medium))
                  .foregroundColor(.cWhite)
                  .padding(.horizontal, 36)
                  .frame(height: 40)
                  .background(Color.accentColor)
                  .cornerRadius(20)
              }
            }
            .transition(.move(edge: .trailing).combined(with: .opacity))
          } else {
            // 查看模式按钮组：退款 + 编辑（或只有编辑）
            HStack(spacing: 32) {
              if transaction.transactionType == .expense {
                Button(action: handleRefundAction) {
                  Text("退款")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(viewModel.canRefund ? .cWhite : .cBlack.opacity(0.4))
                    .padding(.horizontal, 36)
                    .frame(height: 40)
                    .background(viewModel.canRefund ? .cAccentRed : .cBlack.opacity(0.08))
                    .cornerRadius(20)
                }
                .disabled(!viewModel.canRefund)
              }

              Button(action: { viewModel.enterEditingMode() }) {
                Text("编辑")
                  .font(.system(size: 16, weight: .medium))
                  .foregroundColor(.cWhite)
                  .padding(.horizontal, 36)
                  .frame(height: 40)
                  .background(Color.accentColor)
                  .cornerRadius(20)
              }
            }
            .transition(.move(edge: .leading).combined(with: .opacity))
          }

          Spacer()
        }
        .padding(.vertical, 12)
      }
    }
  }
  var body: some View {
    ZStack {
      VStack(spacing: 0) {
        /// 导航栏
        NavigationBarKit(
          title: "交易详情",
          onBack: {
            dataManager.hapticManager.trigger(.selection)
            dismiss()
          },
          rightIcon: "trash-can-simple",
          onRight: {
            dataManager.hapticManager.trigger(.selection)
            if !viewModel.isEditing {  // 编辑模式下禁用删除按钮
              viewModel.showDeleteAlert = true
            }
          },
          rightStyle: .destructive
        )
        .font(.system(size: 15, weight: .medium))
        .foregroundColor(.cBlack)

        /// 顶部区域
        topArea
        ScrollView(.vertical, showsIndicators: false) {
          VStack(spacing: 0) {
            DividerTitleKit(title: "交易类别")
              .padding(.horizontal, 16)
            /// 交易类别
            HStack(spacing: 12) {
              // 使用IconView来正确显示图标（包括图片）
              let categoryInfo = viewModel.getCategoryInfo(
                from: viewModel.isEditing
                  ? viewModel.editingData?.categoryId : transaction.transactionCategoryId,
                transactionType: transaction.transactionType,
                categories: dataManager.mainCategories,
                subCategories: dataManager.subCategories
              )
              IconView(
                viewModel: IconViewVM(
                  icon: categoryInfo.icon,
                  size: 40,
                  fontSize: 20,
                  backgroundColor: .clear,
                  cornerRadius: 12
                ))
              Text(getCurrentDisplayData()?.categoryName ?? getCategoryNameFallback())
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.cBlack)
              Spacer()
              //               退款交易不允许编辑分类，分类应与原始交易保持一致
              if viewModel.isEditing && transaction.transactionType != .refund {
                ActionButton(
                  viewModel: ActionButtonVM.customColor(
                    title: "编辑类别",
                    action: {
                      dataManager.hapticManager.trigger(.selection)
                      viewModel.showCategorySheet = true
                    },
                    textColor: .cAccentBlue,
                    strokeColor: .cAccentBlue.opacity(0.08)
                  )
                )
                .transition(.move(edge: .trailing).combined(with: .opacity))
              }
            }
            .padding(.horizontal, 24)
            //            .padding(.vertical, 12)
            .onTapGesture {
              if viewModel.isEditing && transaction.transactionType != .refund {
                dataManager.hapticManager.trigger(.selection)
                viewModel.showCategorySheet = true
              }
            }

            DividerKit.dashed()
              .padding(.top, 12)
              .padding(.horizontal, 24)
            ///交易明细
            transactionDetail

            // 汇率信息（仅在非本位币时显示）
            if let data = getCurrentDisplayData(),
              let exchangeRate = data.exchangeRateInfo,
              !exchangeRate.isBaseCurrency
            {
              DividerTitleKit(title: "汇率信息")
                .padding(.horizontal, 16)

              // 汇率显示
              HStack(spacing: 0) {
                Text("汇率")
                  .font(.system(size: 14, weight: .regular))
                  .foregroundColor(.cBlack.opacity(0.4))

                Spacer()
                Text(exchangeRate.formattedRate)
                  .font(.system(size: 15, weight: .medium))
                  .foregroundColor(.cBlack)
              }
              .padding(.horizontal, 24)
            }

            DividerTitleKit(title: "卡片详情")
              .padding(.horizontal, 16)

            // 根据卡片显示模式显示不同的布局
            if let data = getCurrentDisplayData() {
              switch data.cardDisplayMode {
              case .single(let card, let type):
                // 单卡片显示
                TransactionCardView(
                  card: card,
                  cardType: type,
                  isEditing: viewModel.isEditing,
                  onEditCard: {
                    dataManager.hapticManager.trigger(.selection)
                    // 根据卡片类型设置编辑类型
                    let cardType: TransactionDetailVM.CardEditType =
                      (type.contains("支出") || type.contains("转出")) ? .from : .to
                    viewModel.currentEditingCardType = cardType
                    viewModel.showCardSheet = true
                  }
                )
              case .transfer(let fromCard, let toCard):
                // 转账双卡片显示
                VStack(spacing: 12) {
                  // 支出卡片
                  TransactionCardView(
                    card: fromCard,
                    cardType: "支出卡片",
                    isEditing: viewModel.isEditing,
                    onEditCard: {
                      dataManager.hapticManager.trigger(.selection)
                      viewModel.currentEditingCardType = .from
                      viewModel.showCardSheet = true
                    }
                  )

                  // 收入卡片
                  TransactionCardView(
                    card: toCard,
                    cardType: "收入卡片",
                    isEditing: viewModel.isEditing,
                    onEditCard: {
                      dataManager.hapticManager.trigger(.selection)
                      viewModel.currentEditingCardType = .to
                      viewModel.showCardSheet = true
                    }
                  )
                }
              }
            } else {
              // 使用交易原始数据作为fallback
              TransactionCardView(
                card: getCardFallback(),
                cardType: getCardTypeFallback(),
                isEditing: false,
                onEditCard: nil
              )
            }

            // 只有在有退款记录时才显示退款记录区域
            if let data = viewModel.detailData, !data.refundTransactions.isEmpty {
              DividerTitleKit(title: "退款记录")
                .padding(.horizontal, 16)

              ForEach(data.refundTransactions, id: \.id) { refundTransaction in
                /// 退款记录（添加导航功能）
                Button(action: {
                  dataManager.hapticManager.trigger(.selection)
                  pathManager.path.append(
                    NavigationDestination.transactionDetailView(refundTransaction.id))
                }) {
                  HStack(spacing: 12) {
                    // 显示退回账户的logo
                    if let refundCard = dataManager.cards.first(where: {
                      $0.id == refundTransaction.toCardId
                    }) {
                      IconView(
                        viewModel: IconViewVM.optionalImage(
                          refundCard.bankLogo,
                          size: 40,
                          style: IconStyle(
                            backgroundColor: .cAccentBlue.opacity(0.1),
                            cornerRadius: 12
                          )
                        )
                      )
                    } else {
                      IconView(
                        viewModel: IconViewVM.optionalImage(
                          nil,
                          size: 40,
                          style: IconStyle(
                            backgroundColor: .cAccentBlue.opacity(0.1),
                            cornerRadius: 12
                          )
                        )
                      )
                    }
                    VStack(alignment: .leading, spacing: 4) {
                      if let refundCard = dataManager.cards.first(where: {
                        $0.id == refundTransaction.toCardId
                      }) {
                        Text(refundCard.name)
                          .font(.system(size: 14, weight: .medium))
                          .foregroundColor(.cBlack)
                      } else {
                        Text("未知卡片")
                          .font(.system(size: 14, weight: .medium))
                          .foregroundColor(.cBlack)
                      }
                      Text("退回卡片")
                        .font(.system(size: 13, weight: .medium))
                        .foregroundColor(.cBlack.opacity(0.6))
                    }
                    Spacer()
                    VStack(alignment: .trailing, spacing: 4) {
                      Text(
                        "\(refundTransaction.symbol)\(NumberFormatService.shared.formatAmount(refundTransaction.transactionAmount))"
                      )
                      .font(.system(size: 14, weight: .medium))
                      .foregroundColor(.cBlack)
                      Text(
                        DateFormattingHelper.shared.smartFormat(
                          date: refundTransaction.transactionDate)
                      )
                      .font(.system(size: 13, weight: .medium))
                      .foregroundColor(.cBlack.opacity(0.6))
                    }
                  }
                  .padding(.horizontal, 24)
                  .padding(.vertical, 12)
                }
                .buttonStyle(PlainButtonStyle())
              }
            }

            // 如果当前是退款交易，显示原始交易信息
            if transaction.transactionType == .refund,
              let originalId = transaction.originalTradId,
              let originalTransaction = dataManager.allTransactions.first(where: {
                $0.id == originalId
              }
              )
            {

              DividerTitleKit(title: "原始交易")
                .padding(.horizontal, 16)

              /// 原始交易（添加导航功能）
              Button(action: {
                dataManager.hapticManager.trigger(.selection)
                pathManager.path.append(
                  NavigationDestination.transactionDetailView(originalTransaction.id))
              }) {
                HStack(spacing: 12) {
                  // 分类图标
                  if let categoryId = originalTransaction.transactionCategoryId {
                    let (_, categoryIcon) = viewModel.getCategoryInfo(
                      from: categoryId,
                      transactionType: originalTransaction.transactionType,
                      categories: dataManager.mainCategories,
                      subCategories: dataManager.subCategories
                    )
                    IconView(
                      viewModel: IconViewVM(
                        icon: categoryIcon,
                        size: 40,
                        fontSize: 20,
                        backgroundColor: .clear,
                        cornerRadius: 12
                      ))
                  } else {
                    IconView(
                      viewModel: IconViewVM.optionalImage(
                        nil,  // 没有分类时传nil，会自动使用question占位符
                        size: 40,
                        style: .clear
                      )
                    )
                  }

                  VStack(alignment: .leading, spacing: 4) {
                    // 显示原始交易的类别名称
                    if let categoryId = originalTransaction.transactionCategoryId {
                      let (categoryName, _) = viewModel.getCategoryInfo(
                        from: categoryId,
                        transactionType: originalTransaction.transactionType,
                        categories: dataManager.mainCategories,
                        subCategories: dataManager.subCategories
                      )
                      Text(categoryName)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.cBlack)
                    } else {
                      Text("未知类别")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.cBlack)
                    }
                    Text(
                      DateFormattingHelper.shared.smartFormat(
                        date: originalTransaction.transactionDate)
                    )
                    .font(.system(size: 13, weight: .regular))
                    .foregroundColor(.cBlack.opacity(0.6))
                  }
                  Spacer()
                  VStack(alignment: .trailing, spacing: 4) {
                    Text(
                      "\(originalTransaction.symbol)\(NumberFormatService.shared.formatAmount(originalTransaction.transactionAmount))"
                    )
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.cBlack)

                    // 显示支出卡片信息
                    if let fromCardId = originalTransaction.fromCardId,
                      let fromCard = dataManager.cards.first(where: { $0.id == fromCardId })
                    {
                      HStack(spacing: 4) {
                        if let imageData = fromCard.bankLogo,
                          let uiImage = UIImage(data: imageData)
                        {
                          Image(uiImage: uiImage)
                            .resizable()
                            .frame(width: 16, height: 16)
                            .cornerRadius(4)
                        } else {
                          Rectangle()
                            .frame(width: 16, height: 16)
                            .foregroundColor(.cAccentBlue.opacity(0.3))
                            .cornerRadius(4)
                        }
                        Text(fromCard.name)
                          .font(.system(size: 13))
                          .foregroundColor(.cBlack.opacity(0.4))
                      }
                    }
                  }
                }
                .padding(.horizontal, 24)
                .padding(.vertical, 12)
              }
              .buttonStyle(PlainButtonStyle())
            }
            Spacer(minLength: 88)
          }
        }

        .background(.cWhite.opacity(0.5))
        .clipShape(
          UnevenRoundedRectangle(topLeadingRadius: 36, topTrailingRadius: 36)
        )
        .edgesIgnoringSafeArea(.bottom)
      }
      actionButtonSection

      // 状态信息区域（加载状态和错误信息）
      statusSection
    }
    .background(
      DottedGridBackground()
        .ignoresSafeArea(.all)  // 让背景延伸到状态栏区域
    )
    .floatingSheet(
      isPresented: $viewModel.showTimeSheet,
      config: SheetBase(
        maxDetent: .fraction(0.45), cornerRadius: 24, interactiveDimiss: false, hPadding: 8,
        bPadding: 4)
    ) {
      SelectTimeSheet(
        viewModel: SelectTimeSheetVM(
          selectedDate: Binding(
            get: { viewModel.editingData?.date ?? Date() },
            set: { viewModel.editingData?.date = $0 }
          ),
          onConfirm: {
            dataManager.hapticManager.trigger(.selection)
            viewModel.showTimeSheet = false
          },
          onCancel: {
            dataManager.hapticManager.trigger(.selection)
            viewModel.showTimeSheet = false
          }
        )
      )
    }
    .floatingSheet(
      isPresented: $viewModel.showNumericKeypad,
      config: SheetBase(
        maxDetent: .height(314),
        cornerRadius: 24,
        interactiveDimiss: false,
        hPadding: 8,
        bPadding: 4
      )
    ) {
      NumericKeypad(
        text: $viewModel.numericKeypadText,
        onSave: {
          dataManager.hapticManager.trigger(.selection)
          viewModel.saveNumericInput()
        },
        allowNegative: transaction.transactionType == .createCard
          || transaction.transactionType == .adjustCard,
        maxDecimalPlaces: 2
      )
    }
    .floatingSheet(
      isPresented: $viewModel.showCategorySheet,
      config: SheetBase(
        maxDetent: .fraction(0.55), cornerRadius: 24, interactiveDimiss: false, hPadding: 8,
        bPadding: 4)
    ) {
      SelectCategorySheet(
        viewModel: SelectCategorySheetVM(
          transactionType: transaction.transactionType,
          selectedCategoryId: Binding(
            get: {
              return viewModel.editingData?.categoryId
            },
            set: { _ in }
          ),
          categories: dataManager.mainCategories,
          subCategories: dataManager.subCategories,
          dataManager: dataManager,
          onCategorySelected: { categoryId in
            dataManager.hapticManager.trigger(.selection)
            viewModel.editingData?.categoryId = categoryId
            viewModel.showCategorySheet = false
          },
          onCancel: {
            dataManager.hapticManager.trigger(.selection)
            viewModel.showCategorySheet = false
          }
        )
      )
    }
    .floatingSheet(
      isPresented: $viewModel.showCardSheet,
      config: SheetBase(
        maxDetent: .fraction(0.45), cornerRadius: 24, interactiveDimiss: false, hPadding: 8,
        bPadding: 4)
    ) {
      SelectCardSheet(
        viewModel: SelectCardSheetVM(
          selectedCardId: Binding(
            get: {
              switch transaction.transactionType {
              case .expense:
                return viewModel.editingData?.fromCardId
              case .income, .refund:
                return viewModel.editingData?.toCardId
              case .transfer:
                return viewModel.currentEditingCardType == .from
                  ? viewModel.editingData?.fromCardId : viewModel.editingData?.toCardId
              case .createCard:
                return viewModel.editingData?.toCardId
              case .adjustCard:
                return viewModel.editingData?.fromCardId ?? viewModel.editingData?.toCardId
              }
            },
            set: { _ in }
          ),
          cards: dataManager.cards,
          dataManager: dataManager,
          onCardSelected: { cardId in
            dataManager.hapticManager.trigger(.selection)
            viewModel.saveCardSelection(cardId: cardId)
          },
          onCancel: {
            dataManager.hapticManager.trigger(.selection)
            viewModel.showCardSheet = false
          }
        )
      )
    }

    .navigationBarTitleDisplayMode(.inline)
    .navigationBarBackButtonHidden(true)

    .animation(.easeInOut(duration: 0.3), value: viewModel.isEditing)
    .alert("删除交易", isPresented: $viewModel.showDeleteAlert) {
      Button("取消", role: .cancel) {
        dataManager.hapticManager.trigger(.selection)
      }
      Button("删除", role: .destructive) {
        dataManager.hapticManager.trigger(.selection)
        handleDeleteAction()
      }
    } message: {
      Text("确定要删除这笔交易吗？此操作不可撤销。")
    }
    .alert("删除失败", isPresented: $viewModel.showDeleteErrorAlert) {
      Button("确定", role: .cancel) {
        dataManager.hapticManager.trigger(.selection)
      }
    } message: {
      Text(viewModel.deleteErrorMessage)
    }
  }

  // MARK: - 状态信息区域
  /// 状态信息区域（错误信息）
  private var statusSection: some View {
    Group {
      // 显示错误信息
      if let errorMessage = viewModel.errorMessage {
        Text(errorMessage)
          .font(.system(size: 14))
          .foregroundColor(.clear)
          .padding()
      }
    }
  }

  /// 获取分类名称的fallback
  private func getCategoryNameFallback() -> String {
    if let categoryId = transaction.transactionCategoryId {
      let (categoryName, _) = viewModel.getCategoryInfo(
        from: categoryId,
        transactionType: transaction.transactionType,
        categories: dataManager.mainCategories,
        subCategories: dataManager.subCategories
      )
      return categoryName
    }
    return "未分类"
  }

  /// 获取卡片的fallback
  private func getCardFallback() -> CardModel? {
    if transaction.transactionType == .expense || transaction.transactionType == .transfer {
      return dataManager.cards.first { $0.id == transaction.fromCardId }
    } else {
      return dataManager.cards.first { $0.id == transaction.toCardId }
    }
  }

  /// 获取卡片类型的fallback
  private func getCardTypeFallback() -> String {
    switch transaction.transactionType {
    case .expense:
      return "支出卡片"
    case .income:
      return "收入卡片"
    case .transfer:
      return "支出卡片"
    case .refund:
      return "退款卡片"
    case .createCard:
      return "创建卡片"
    case .adjustCard:
      return "调整卡片"
    }
  }

  // MARK: - 私有方法

  /// 处理退款操作
  private func handleRefundAction() {
    if let destination = viewModel.handleRefundAction() {
      pathManager.path.append(destination)
    }
  }

  /// 获取当前显示的数据（编辑状态下使用编辑数据，否则使用原始数据）
  private func getCurrentDisplayData() -> TransactionDetailVM.TransactionDetailData? {
    return viewModel.currentDisplayData
  }

  /// 获取过滤后的类别列表
  private func getFilteredCategories() -> [TransactionMainCategoryModel] {
    return viewModel.filteredCategories
  }

  /// 处理取消操作
  private func handleCancelAction() {
    // 取消编辑
    viewModel.cancelEditing()
  }

  /// 处理保存操作
  private func handleSaveAction() {
    if viewModel.handleSaveAction(modelContext: modelContext) {
      // 保存成功，触觉反馈
      dataManager.hapticManager.trigger(.selection)
    }
  }

  /// 处理删除操作
  private func handleDeleteAction() {
    // 先返回上一页，避免用户看到空状态
    dismiss()

    // 延迟执行删除操作，确保页面已经返回
    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
      let _ = viewModel.handleDeleteAction(modelContext: modelContext)
    }
  }

}

// MARK: - Preview Provider

#if DEBUG
  struct TransactionDetailView_Previews: PreviewProvider {
    static var previews: some View {
      Group {
        // 场景1: 支出交易 - 带优惠和退款
        TransactionDetailView(
          transaction: createExpenseTransaction(),
          dataManager: createMockDataManager()
        )
        .previewDisplayName("支出交易 - 购物")

        // 场景2: 收入交易
        TransactionDetailView(
          transaction: createIncomeTransaction(),
          dataManager: createMockDataManager()
        )
        .previewDisplayName("收入交易 - 工资")

        // 场景3: 转账交易
        TransactionDetailView(
          transaction: createTransferTransaction(),
          dataManager: createMockDataManager()
        )
        .previewDisplayName("转账交易")

        // 场景4: 退款交易
        TransactionDetailView(
          transaction: createRefundTransaction(),
          dataManager: createMockDataManager()
        )
        .previewDisplayName("退款交易")

        // 场景5: 美元支出交易（多币种）
        TransactionDetailView(
          transaction: createUSDExpenseTransaction(),
          dataManager: createMockDataManager()
        )
        .previewDisplayName("美元支出 - 海外购物")
      }
    }

    // MARK: - Mock Data Creation Methods

    /// 创建支出交易（购物 - 带优惠和退款）
    private static func createExpenseTransaction() -> TransactionModel {
      TransactionModel(
        id: UUID(),
        transactionType: .expense,
        transactionCategoryId: "shopping_daily",
        fromCardId: UUID(uuidString: "11111111-1111-1111-1111-111111111111")!,
        discountAmount: 20.0,
        transactionAmount: 299.99,
        refundAmount: 50.0,
        currency: "CNY",
        symbol: "¥",
        expenseToCardRate: 1.0,
        expenseToBaseRate: 1.0,
        incomeToCardRate: 1.0,
        incomeToBaseRate: 1.0,
        isStatistics: true,
        remark: "天猫双11购物 - 冬季外套，质量很好，推荐购买！",
        transactionDate: Calendar.current.date(byAdding: .day, value: -1, to: Date()) ?? Date(),
        createdAt: Date(),
        updatedAt: Date()
      )
    }

    /// 创建收入交易（工资）
    private static func createIncomeTransaction() -> TransactionModel {
      TransactionModel(
        id: UUID(),
        transactionType: .income,
        transactionCategoryId: "salary",
        toCardId: UUID(uuidString: "*************-2222-2222-************")!,
        transactionAmount: 15000.0,
        currency: "CNY",
        symbol: "¥",
        expenseToCardRate: 1.0,
        expenseToBaseRate: 1.0,
        incomeToCardRate: 1.0,
        incomeToBaseRate: 1.0,
        isStatistics: true,
        remark: "2024年1月工资收入",
        transactionDate: Calendar.current.date(byAdding: .day, value: -3, to: Date()) ?? Date(),
        createdAt: Date(),
        updatedAt: Date()
      )
    }

    /// 创建转账交易
    private static func createTransferTransaction() -> TransactionModel {
      TransactionModel(
        id: UUID(),
        transactionType: .transfer,
        fromCardId: UUID(uuidString: "*************-2222-2222-************")!,
        toCardId: UUID(uuidString: "*************-3333-3333-************")!,
        transactionAmount: 5000.0,
        currency: "CNY",
        symbol: "¥",
        expenseToCardRate: 1.0,
        expenseToBaseRate: 1.0,
        incomeToCardRate: 1.0,
        incomeToBaseRate: 1.0,
        isStatistics: false,
        remark: "还信用卡账单",
        transactionDate: Calendar.current.date(byAdding: .hour, value: -6, to: Date()) ?? Date(),
        createdAt: Date(),
        updatedAt: Date()
      )
    }

    /// 创建退款交易
    private static func createRefundTransaction() -> TransactionModel {
      TransactionModel(
        id: UUID(),
        originalTransactionId: UUID(uuidString: "*************-4444-4444-************")!,
        transactionType: .refund,
        transactionCategoryId: "shopping_electronics",
        toCardId: UUID(uuidString: "11111111-1111-1111-1111-111111111111")!,
        transactionAmount: 1299.0,
        currency: "CNY",
        symbol: "¥",
        expenseToCardRate: 1.0,
        expenseToBaseRate: 1.0,
        incomeToCardRate: 1.0,
        incomeToBaseRate: 1.0,
        isStatistics: true,
        remark: "iPhone手机退货 - 不满意外观颜色",
        originalTradId: UUID(uuidString: "*************-4444-4444-************")!,
        transactionDate: Calendar.current.date(byAdding: .day, value: -2, to: Date()) ?? Date(),
        createdAt: Date(),
        updatedAt: Date()
      )
    }

    /// 创建美元支出交易（多币种场景）
    private static func createUSDExpenseTransaction() -> TransactionModel {
      TransactionModel(
        id: UUID(),
        transactionType: .expense,
        transactionCategoryId: "travel",
        fromCardId: UUID(uuidString: "*************-3333-3333-************")!,
        transactionAmount: 89.99,
        currency: "USD",
        symbol: "$",
        expenseToCardRate: 7.25,  // 美元到人民币汇率
        expenseToBaseRate: 7.25,
        incomeToCardRate: 1.0,
        incomeToBaseRate: 1.0,
        isStatistics: true,
        remark: "美国旅游 - 酒店住宿费用",
        transactionDate: Calendar.current.date(byAdding: .day, value: -7, to: Date()) ?? Date(),
        createdAt: Date(),
        updatedAt: Date()
      )
    }

    /// 创建模拟数据管理器
    private static func createMockDataManager() -> DataManagement {
      // 使用空的数据管理器，在预览中主要用于避免崩溃
      // 实际的数据渲染可能不完整，但足够用于UI预览
      return DataManagement(
        cards: [],
        mainCategories: [],
        subCategories: [],
        currencies: [],
        recentTransactions: [],
        allTransactions: [],
        chatMessages: []
      )
    }
  }
#endif
