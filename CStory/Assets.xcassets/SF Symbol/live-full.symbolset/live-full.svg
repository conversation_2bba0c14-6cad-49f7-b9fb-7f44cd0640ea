<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg
PUBLIC "-//W3C//DTD SVG 1.1//EN"
     "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
     <!--Created with SF Symbol Generator (v1.0.0)-->

<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="3300" height="2200">
<!--glyph: "live-full.medium", point size: 100.0-->
<style>.SFSymbolsPreviewWireframe {fill:none;opacity:1.0;stroke:black;stroke-width:0.5}
</style>
<g id="Notes">
<rect height="2200" id="artboard" style="fill:white;opacity:1" width="3300" x="0" y="0"/>
<line style="fill:none;stroke:black;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="292" y2="292"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 263 322)">Weight/Scale Variations</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 559.711 322)">Ultralight</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 856.422 322)">Thin</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1153.13 322)">Light</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1449.84 322)">Regular</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1746.56 322)">Medium</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2043.27 322)">Semibold</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2339.98 322)">Bold</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2636.69 322)">Heavy</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2933.4 322)">Black</text>
<line style="fill:none;stroke:black;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1903" y2="1903"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 263 1953)">Design Variations</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1971)">Symbols are supported in up to nine weights and three scales.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1989)">For optimal layout with text and other symbols, vertically align</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 2007)">symbols with the adjacent text.</text>
<line style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="776" x2="776" y1="1919" y2="1933"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 776 1953)">Margins</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 1971)">Leading and trailing margins on the left and right side of each symbol</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 1989)">can be adjusted by modifying the x-location of the margin guidelines.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 2007)">Modifications are automatically applied proportionally to all</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 2025)">scales and weights.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 1289 1953)">Exporting</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 1289 1971)">Symbols should be outlined when exporting to ensure the</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 1289 1989)">design is preserved when submitting to Xcode.</text>
<text id="template-version" style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1933)">Template v.5.0</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1951)">Requires Xcode 15 or greater</text>
<text id="descriptive-name" style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1969)">Generated from live-full</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1987)">Typeset at 100.0 points</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 726)">Small</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1156)">Medium</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1586)">Large</text>
</g>
<g id="Guides">
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 696)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-S" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="696" y2="696"/>
<line id="Capline-S" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="625.541" y2="625.541"/>
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 1126)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-M" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1126" y2="1126"/>
<line id="Capline-M" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1055.54" y2="1055.54"/>
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 1556)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-L" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1556" y2="1556"/>
<line id="Capline-L" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1485.54" y2="1485.54"/>
<line id="left-margin-Ultralight-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="513.711" x2="513.711" y1="600.785" y2="720.121"/>
<line id="right-margin-Ultralight-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="605.711" x2="605.711" y1="600.785" y2="720.121"/>
<line id="left-margin-Regular-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="1403.84" x2="1403.84" y1="600.785" y2="720.121"/>
<line id="right-margin-Regular-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="1495.84" x2="1495.84" y1="600.785" y2="720.121"/>
<line id="left-margin-Black-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="2887.4" x2="2887.4" y1="600.785" y2="720.121"/>
<line id="right-margin-Black-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="2979.4" x2="2979.4" y1="600.785" y2="720.121"/>
</g>
<g id="Symbols">
<g id="Ultralight-S" transform="matrix(1 0 0 1 513.711 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="live-full">
<g id="Union">
<path d="M18.8934 18.8976C20.016 17.775 21.836 17.7754 22.9588 18.8976C24.0813 20.0204 24.0815 21.8404 22.9588 22.963C17.0597 28.8626 13.4167 37.0062 13.4167 46.0042C13.4171 55.0013 17.0603 63.1426 22.9588 69.0417C24.0813 70.1644 24.0813 71.9844 22.9588 73.1071C21.8361 74.2295 20.0161 74.2295 18.8934 73.1071C11.9606 66.1738 7.66708 56.5876 7.66667 46.0042C7.66667 35.4199 11.96 25.8314 18.8934 18.8976Z" fill="black"/>
<path d="M69.0412 18.8976C70.164 17.7755 71.984 17.7751 73.1066 18.8976C80.0403 25.8314 84.3333 35.42 84.3333 46.0042C84.3329 56.5875 80.0396 66.1737 73.1066 73.1071C71.9839 74.2295 70.1638 74.2296 69.0412 73.1071C67.9186 71.9844 67.9187 70.1644 69.0412 69.0417C74.9399 63.1427 78.5829 55.0014 78.5833 46.0042C78.5833 37.0062 74.9405 28.8625 69.0412 22.963C67.9185 21.8404 67.9187 20.0204 69.0412 18.8976Z" fill="black"/>
<path d="M30.0378 30.042C31.1605 28.9192 32.9804 28.9192 34.1032 30.042C35.2254 31.1648 35.2258 32.9849 34.1032 34.1074C31.0559 37.155 29.1768 41.3578 29.1768 46.0042C29.1772 50.6504 31.0559 54.8539 34.1032 57.901C35.2251 59.0238 35.2256 60.844 34.1032 61.9665C32.9806 63.0879 31.1603 63.088 30.0378 61.9665C25.9561 57.885 23.4272 52.2366 23.4268 46.0042C23.4268 39.7716 25.9562 34.1239 30.0378 30.042Z" fill="black"/>
<path d="M57.8968 30.042C59.0195 28.9192 60.8395 28.9193 61.9622 30.042C66.0439 34.1239 68.5732 39.7717 68.5732 46.0042C68.5728 52.2366 66.0441 57.885 61.9622 61.9665C60.8398 63.0881 59.0194 63.0878 57.8968 61.9665C56.7744 60.8439 56.7749 59.0238 57.8968 57.901C60.9442 54.8539 62.8228 50.6504 62.8232 46.0042C62.8232 41.3579 60.9442 37.155 57.8968 34.1074C56.7742 32.9848 56.7745 31.1648 57.8968 30.042Z" fill="black"/>
<path d="M46 40.2505C49.1753 40.2505 51.75 42.8243 51.75 46.0005C51.7497 49.1758 49.1754 51.7505 46 51.7505C42.8246 51.7505 40.2503 49.1758 40.25 46.0005C40.25 42.8243 42.8247 40.2505 46 40.2505Z" fill="black"/>
</g>
</g>
</svg>

</g>
<g id="Regular-S" transform="matrix(1 0 0 1 1403.84 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="live-full">
<g id="Union">
<path d="M18.8934 18.8976C20.016 17.775 21.836 17.7754 22.9588 18.8976C24.0813 20.0204 24.0815 21.8404 22.9588 22.963C17.0597 28.8626 13.4167 37.0062 13.4167 46.0042C13.4171 55.0013 17.0603 63.1426 22.9588 69.0417C24.0813 70.1644 24.0813 71.9844 22.9588 73.1071C21.8361 74.2295 20.0161 74.2295 18.8934 73.1071C11.9606 66.1738 7.66708 56.5876 7.66667 46.0042C7.66667 35.4199 11.96 25.8314 18.8934 18.8976Z" fill="black"/>
<path d="M69.0412 18.8976C70.164 17.7755 71.984 17.7751 73.1066 18.8976C80.0403 25.8314 84.3333 35.42 84.3333 46.0042C84.3329 56.5875 80.0396 66.1737 73.1066 73.1071C71.9839 74.2295 70.1638 74.2296 69.0412 73.1071C67.9186 71.9844 67.9187 70.1644 69.0412 69.0417C74.9399 63.1427 78.5829 55.0014 78.5833 46.0042C78.5833 37.0062 74.9405 28.8625 69.0412 22.963C67.9185 21.8404 67.9187 20.0204 69.0412 18.8976Z" fill="black"/>
<path d="M30.0378 30.042C31.1605 28.9192 32.9804 28.9192 34.1032 30.042C35.2254 31.1648 35.2258 32.9849 34.1032 34.1074C31.0559 37.155 29.1768 41.3578 29.1768 46.0042C29.1772 50.6504 31.0559 54.8539 34.1032 57.901C35.2251 59.0238 35.2256 60.844 34.1032 61.9665C32.9806 63.0879 31.1603 63.088 30.0378 61.9665C25.9561 57.885 23.4272 52.2366 23.4268 46.0042C23.4268 39.7716 25.9562 34.1239 30.0378 30.042Z" fill="black"/>
<path d="M57.8968 30.042C59.0195 28.9192 60.8395 28.9193 61.9622 30.042C66.0439 34.1239 68.5732 39.7717 68.5732 46.0042C68.5728 52.2366 66.0441 57.885 61.9622 61.9665C60.8398 63.0881 59.0194 63.0878 57.8968 61.9665C56.7744 60.8439 56.7749 59.0238 57.8968 57.901C60.9442 54.8539 62.8228 50.6504 62.8232 46.0042C62.8232 41.3579 60.9442 37.155 57.8968 34.1074C56.7742 32.9848 56.7745 31.1648 57.8968 30.042Z" fill="black"/>
<path d="M46 40.2505C49.1753 40.2505 51.75 42.8243 51.75 46.0005C51.7497 49.1758 49.1754 51.7505 46 51.7505C42.8246 51.7505 40.2503 49.1758 40.25 46.0005C40.25 42.8243 42.8247 40.2505 46 40.2505Z" fill="black"/>
</g>
</g>
</svg>

</g>
<g id="Black-S" transform="matrix(1 0 0 1 2887.4 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="live-full">
<g id="Union">
<path d="M18.8934 18.8976C20.016 17.775 21.836 17.7754 22.9588 18.8976C24.0813 20.0204 24.0815 21.8404 22.9588 22.963C17.0597 28.8626 13.4167 37.0062 13.4167 46.0042C13.4171 55.0013 17.0603 63.1426 22.9588 69.0417C24.0813 70.1644 24.0813 71.9844 22.9588 73.1071C21.8361 74.2295 20.0161 74.2295 18.8934 73.1071C11.9606 66.1738 7.66708 56.5876 7.66667 46.0042C7.66667 35.4199 11.96 25.8314 18.8934 18.8976Z" fill="black"/>
<path d="M69.0412 18.8976C70.164 17.7755 71.984 17.7751 73.1066 18.8976C80.0403 25.8314 84.3333 35.42 84.3333 46.0042C84.3329 56.5875 80.0396 66.1737 73.1066 73.1071C71.9839 74.2295 70.1638 74.2296 69.0412 73.1071C67.9186 71.9844 67.9187 70.1644 69.0412 69.0417C74.9399 63.1427 78.5829 55.0014 78.5833 46.0042C78.5833 37.0062 74.9405 28.8625 69.0412 22.963C67.9185 21.8404 67.9187 20.0204 69.0412 18.8976Z" fill="black"/>
<path d="M30.0378 30.042C31.1605 28.9192 32.9804 28.9192 34.1032 30.042C35.2254 31.1648 35.2258 32.9849 34.1032 34.1074C31.0559 37.155 29.1768 41.3578 29.1768 46.0042C29.1772 50.6504 31.0559 54.8539 34.1032 57.901C35.2251 59.0238 35.2256 60.844 34.1032 61.9665C32.9806 63.0879 31.1603 63.088 30.0378 61.9665C25.9561 57.885 23.4272 52.2366 23.4268 46.0042C23.4268 39.7716 25.9562 34.1239 30.0378 30.042Z" fill="black"/>
<path d="M57.8968 30.042C59.0195 28.9192 60.8395 28.9193 61.9622 30.042C66.0439 34.1239 68.5732 39.7717 68.5732 46.0042C68.5728 52.2366 66.0441 57.885 61.9622 61.9665C60.8398 63.0881 59.0194 63.0878 57.8968 61.9665C56.7744 60.8439 56.7749 59.0238 57.8968 57.901C60.9442 54.8539 62.8228 50.6504 62.8232 46.0042C62.8232 41.3579 60.9442 37.155 57.8968 34.1074C56.7742 32.9848 56.7745 31.1648 57.8968 30.042Z" fill="black"/>
<path d="M46 40.2505C49.1753 40.2505 51.75 42.8243 51.75 46.0005C51.7497 49.1758 49.1754 51.7505 46 51.7505C42.8246 51.7505 40.2503 49.1758 40.25 46.0005C40.25 42.8243 42.8247 40.2505 46 40.2505Z" fill="black"/>
</g>
</g>
</svg>

</g>
</g>
</svg>