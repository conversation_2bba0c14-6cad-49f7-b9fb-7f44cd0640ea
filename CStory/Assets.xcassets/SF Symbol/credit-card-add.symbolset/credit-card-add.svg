<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg
PUBLIC "-//W3C//DTD SVG 1.1//EN"
     "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
     <!--Created with SF Symbol Generator (v1.0.0)-->

<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="3300" height="2200">
<!--glyph: "credit-card-add.medium", point size: 100.0-->
<style>.SFSymbolsPreviewWireframe {fill:none;opacity:1.0;stroke:black;stroke-width:0.5}
</style>
<g id="Notes">
<rect height="2200" id="artboard" style="fill:white;opacity:1" width="3300" x="0" y="0"/>
<line style="fill:none;stroke:black;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="292" y2="292"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 263 322)">Weight/Scale Variations</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 559.711 322)">Ultralight</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 856.422 322)">Thin</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1153.13 322)">Light</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1449.84 322)">Regular</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1746.56 322)">Medium</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2043.27 322)">Semibold</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2339.98 322)">Bold</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2636.69 322)">Heavy</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2933.4 322)">Black</text>
<line style="fill:none;stroke:black;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1903" y2="1903"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 263 1953)">Design Variations</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1971)">Symbols are supported in up to nine weights and three scales.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1989)">For optimal layout with text and other symbols, vertically align</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 2007)">symbols with the adjacent text.</text>
<line style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="776" x2="776" y1="1919" y2="1933"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 776 1953)">Margins</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 1971)">Leading and trailing margins on the left and right side of each symbol</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 1989)">can be adjusted by modifying the x-location of the margin guidelines.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 2007)">Modifications are automatically applied proportionally to all</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 2025)">scales and weights.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 1289 1953)">Exporting</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 1289 1971)">Symbols should be outlined when exporting to ensure the</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 1289 1989)">design is preserved when submitting to Xcode.</text>
<text id="template-version" style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1933)">Template v.5.0</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1951)">Requires Xcode 15 or greater</text>
<text id="descriptive-name" style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1969)">Generated from credit-card-add</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1987)">Typeset at 100.0 points</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 726)">Small</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1156)">Medium</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1586)">Large</text>
</g>
<g id="Guides">
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 696)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-S" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="696" y2="696"/>
<line id="Capline-S" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="625.541" y2="625.541"/>
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 1126)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-M" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1126" y2="1126"/>
<line id="Capline-M" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1055.54" y2="1055.54"/>
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 1556)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-L" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1556" y2="1556"/>
<line id="Capline-L" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1485.54" y2="1485.54"/>
<line id="left-margin-Ultralight-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="513.711" x2="513.711" y1="600.785" y2="720.121"/>
<line id="right-margin-Ultralight-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="605.711" x2="605.711" y1="600.785" y2="720.121"/>
<line id="left-margin-Regular-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="1403.84" x2="1403.84" y1="600.785" y2="720.121"/>
<line id="right-margin-Regular-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="1495.84" x2="1495.84" y1="600.785" y2="720.121"/>
<line id="left-margin-Black-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="2887.4" x2="2887.4" y1="600.785" y2="720.121"/>
<line id="right-margin-Black-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="2979.4" x2="2979.4" y1="600.785" y2="720.121"/>
</g>
<g id="Symbols">
<g id="Ultralight-S" transform="matrix(1 0 0 1 513.711 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="credit-card-add">
<g id="Union">
<path d="M73.7917 51.75C75.3795 51.75 76.6667 53.0372 76.6667 54.625V63.25H85.2917C86.8795 63.25 88.1667 64.5372 88.1667 66.125C88.1667 67.7128 86.8795 69 85.2917 69H76.6667V77.625C76.6667 79.2128 75.3795 80.5 73.7917 80.5C72.2038 80.5 70.9167 79.2128 70.9167 77.625V69H62.2917C60.7038 69 59.4167 67.7128 59.4167 66.125C59.4167 64.5372 60.7038 63.25 62.2917 63.25H70.9167V54.625C70.9167 53.0372 72.2038 51.75 73.7917 51.75Z" fill="black"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M66.14 15.3446C76.1745 15.3448 84.3185 23.4656 84.3184 33.508V43.1175C84.3184 44.7053 83.0312 45.9925 81.4434 45.9925C79.8555 45.9925 78.5684 44.7053 78.5684 43.1175V40.25H13.4167V58.4434C13.4167 65.3241 18.9944 70.9017 25.875 70.9017H51.735C53.3228 70.9017 54.61 72.1889 54.61 73.7767C54.61 75.3645 53.3228 76.6517 51.735 76.6517H25.875C15.8188 76.6517 7.66667 68.4998 7.66667 58.4434V33.5529C7.66667 23.4967 15.8188 15.3446 25.875 15.3446H66.14ZM25.875 21.0946C18.9944 21.0946 13.4167 26.6723 13.4167 33.5529V34.5H78.5684V33.508C78.5685 26.6491 73.0067 21.0948 66.14 21.0946H25.875Z" fill="black"/>
</g>
</g>
</svg>

</g>
<g id="Regular-S" transform="matrix(1 0 0 1 1403.84 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="credit-card-add">
<g id="Union">
<path d="M73.7917 51.75C75.3795 51.75 76.6667 53.0372 76.6667 54.625V63.25H85.2917C86.8795 63.25 88.1667 64.5372 88.1667 66.125C88.1667 67.7128 86.8795 69 85.2917 69H76.6667V77.625C76.6667 79.2128 75.3795 80.5 73.7917 80.5C72.2038 80.5 70.9167 79.2128 70.9167 77.625V69H62.2917C60.7038 69 59.4167 67.7128 59.4167 66.125C59.4167 64.5372 60.7038 63.25 62.2917 63.25H70.9167V54.625C70.9167 53.0372 72.2038 51.75 73.7917 51.75Z" fill="black"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M66.14 15.3446C76.1745 15.3448 84.3185 23.4656 84.3184 33.508V43.1175C84.3184 44.7053 83.0312 45.9925 81.4434 45.9925C79.8555 45.9925 78.5684 44.7053 78.5684 43.1175V40.25H13.4167V58.4434C13.4167 65.3241 18.9944 70.9017 25.875 70.9017H51.735C53.3228 70.9017 54.61 72.1889 54.61 73.7767C54.61 75.3645 53.3228 76.6517 51.735 76.6517H25.875C15.8188 76.6517 7.66667 68.4998 7.66667 58.4434V33.5529C7.66667 23.4967 15.8188 15.3446 25.875 15.3446H66.14ZM25.875 21.0946C18.9944 21.0946 13.4167 26.6723 13.4167 33.5529V34.5H78.5684V33.508C78.5685 26.6491 73.0067 21.0948 66.14 21.0946H25.875Z" fill="black"/>
</g>
</g>
</svg>

</g>
<g id="Black-S" transform="matrix(1 0 0 1 2887.4 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="credit-card-add">
<g id="Union">
<path d="M73.7917 51.75C75.3795 51.75 76.6667 53.0372 76.6667 54.625V63.25H85.2917C86.8795 63.25 88.1667 64.5372 88.1667 66.125C88.1667 67.7128 86.8795 69 85.2917 69H76.6667V77.625C76.6667 79.2128 75.3795 80.5 73.7917 80.5C72.2038 80.5 70.9167 79.2128 70.9167 77.625V69H62.2917C60.7038 69 59.4167 67.7128 59.4167 66.125C59.4167 64.5372 60.7038 63.25 62.2917 63.25H70.9167V54.625C70.9167 53.0372 72.2038 51.75 73.7917 51.75Z" fill="black"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M66.14 15.3446C76.1745 15.3448 84.3185 23.4656 84.3184 33.508V43.1175C84.3184 44.7053 83.0312 45.9925 81.4434 45.9925C79.8555 45.9925 78.5684 44.7053 78.5684 43.1175V40.25H13.4167V58.4434C13.4167 65.3241 18.9944 70.9017 25.875 70.9017H51.735C53.3228 70.9017 54.61 72.1889 54.61 73.7767C54.61 75.3645 53.3228 76.6517 51.735 76.6517H25.875C15.8188 76.6517 7.66667 68.4998 7.66667 58.4434V33.5529C7.66667 23.4967 15.8188 15.3446 25.875 15.3446H66.14ZM25.875 21.0946C18.9944 21.0946 13.4167 26.6723 13.4167 33.5529V34.5H78.5684V33.508C78.5685 26.6491 73.0067 21.0948 66.14 21.0946H25.875Z" fill="black"/>
</g>
</g>
</svg>

</g>
</g>
</svg>