<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg
PUBLIC "-//W3C//DTD SVG 1.1//EN"
     "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
     <!--Created with SF Symbol Generator (v1.0.0)-->

<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="3300" height="2200">
<!--glyph: "trading-view-candles.medium", point size: 100.0-->
<style>.SFSymbolsPreviewWireframe {fill:none;opacity:1.0;stroke:black;stroke-width:0.5}
</style>
<g id="Notes">
<rect height="2200" id="artboard" style="fill:white;opacity:1" width="3300" x="0" y="0"/>
<line style="fill:none;stroke:black;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="292" y2="292"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 263 322)">Weight/Scale Variations</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 559.711 322)">Ultralight</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 856.422 322)">Thin</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1153.13 322)">Light</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1449.84 322)">Regular</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1746.56 322)">Medium</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2043.27 322)">Semibold</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2339.98 322)">Bold</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2636.69 322)">Heavy</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2933.4 322)">Black</text>
<line style="fill:none;stroke:black;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1903" y2="1903"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 263 1953)">Design Variations</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1971)">Symbols are supported in up to nine weights and three scales.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1989)">For optimal layout with text and other symbols, vertically align</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 2007)">symbols with the adjacent text.</text>
<line style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="776" x2="776" y1="1919" y2="1933"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 776 1953)">Margins</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 1971)">Leading and trailing margins on the left and right side of each symbol</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 1989)">can be adjusted by modifying the x-location of the margin guidelines.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 2007)">Modifications are automatically applied proportionally to all</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 2025)">scales and weights.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 1289 1953)">Exporting</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 1289 1971)">Symbols should be outlined when exporting to ensure the</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 1289 1989)">design is preserved when submitting to Xcode.</text>
<text id="template-version" style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1933)">Template v.5.0</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1951)">Requires Xcode 15 or greater</text>
<text id="descriptive-name" style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1969)">Generated from trading-view-candles</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1987)">Typeset at 100.0 points</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 726)">Small</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1156)">Medium</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1586)">Large</text>
</g>
<g id="Guides">
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 696)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-S" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="696" y2="696"/>
<line id="Capline-S" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="625.541" y2="625.541"/>
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 1126)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-M" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1126" y2="1126"/>
<line id="Capline-M" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1055.54" y2="1055.54"/>
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 1556)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-L" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1556" y2="1556"/>
<line id="Capline-L" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1485.54" y2="1485.54"/>
<line id="left-margin-Ultralight-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="513.711" x2="513.711" y1="600.785" y2="720.121"/>
<line id="right-margin-Ultralight-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="605.711" x2="605.711" y1="600.785" y2="720.121"/>
<line id="left-margin-Regular-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="1403.84" x2="1403.84" y1="600.785" y2="720.121"/>
<line id="right-margin-Regular-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="1495.84" x2="1495.84" y1="600.785" y2="720.121"/>
<line id="left-margin-Black-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="2887.4" x2="2887.4" y1="600.785" y2="720.121"/>
<line id="right-margin-Black-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="2979.4" x2="2979.4" y1="600.785" y2="720.121"/>
</g>
<g id="Symbols">
<g id="Ultralight-S" transform="matrix(1 0 0 1 513.711 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="trading-view-candles">
<g id="Union">
<path d="M26.8333 7.66667C28.4211 7.66667 29.7083 8.95386 29.7083 10.5417V38.4606C36.7412 39.3979 42.1667 45.4193 42.1667 52.7083V58.4583C42.1667 65.7474 36.7412 71.7688 29.7083 72.7061V81.4583C29.7083 83.0461 28.4211 84.3333 26.8333 84.3333C25.2455 84.3333 23.9583 83.0461 23.9583 81.4583V72.7061C16.9254 71.7688 11.5 65.7474 11.5 58.4583V52.7083C11.5 45.4193 16.9254 39.3979 23.9583 38.4606V10.5417C23.9583 8.95386 25.2455 7.66667 26.8333 7.66667Z" fill="black"/>
<path d="M65.1667 7.66667C66.7544 7.66667 68.0416 8.95388 68.0417 10.5417V19.2939C75.0746 20.231 80.5 26.2525 80.5 33.5417V58.4583C80.5 65.7474 75.0746 71.7688 68.0417 72.7061V81.4583C68.0417 83.0461 66.7544 84.3333 65.1667 84.3333C63.5789 84.3333 62.2917 83.0461 62.2917 81.4583V72.7061C55.2587 71.7688 49.8333 65.7474 49.8333 58.4583V33.5417C49.8333 26.2525 55.2587 20.231 62.2917 19.2939V10.5417C62.2917 8.95388 63.5789 7.66667 65.1667 7.66667Z" fill="black"/>
</g>
</g>
</svg>

</g>
<g id="Regular-S" transform="matrix(1 0 0 1 1403.84 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="trading-view-candles">
<g id="Union">
<path d="M26.8333 7.66667C28.4211 7.66667 29.7083 8.95386 29.7083 10.5417V38.4606C36.7412 39.3979 42.1667 45.4193 42.1667 52.7083V58.4583C42.1667 65.7474 36.7412 71.7688 29.7083 72.7061V81.4583C29.7083 83.0461 28.4211 84.3333 26.8333 84.3333C25.2455 84.3333 23.9583 83.0461 23.9583 81.4583V72.7061C16.9254 71.7688 11.5 65.7474 11.5 58.4583V52.7083C11.5 45.4193 16.9254 39.3979 23.9583 38.4606V10.5417C23.9583 8.95386 25.2455 7.66667 26.8333 7.66667Z" fill="black"/>
<path d="M65.1667 7.66667C66.7544 7.66667 68.0416 8.95388 68.0417 10.5417V19.2939C75.0746 20.231 80.5 26.2525 80.5 33.5417V58.4583C80.5 65.7474 75.0746 71.7688 68.0417 72.7061V81.4583C68.0417 83.0461 66.7544 84.3333 65.1667 84.3333C63.5789 84.3333 62.2917 83.0461 62.2917 81.4583V72.7061C55.2587 71.7688 49.8333 65.7474 49.8333 58.4583V33.5417C49.8333 26.2525 55.2587 20.231 62.2917 19.2939V10.5417C62.2917 8.95388 63.5789 7.66667 65.1667 7.66667Z" fill="black"/>
</g>
</g>
</svg>

</g>
<g id="Black-S" transform="matrix(1 0 0 1 2887.4 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="trading-view-candles">
<g id="Union">
<path d="M26.8333 7.66667C28.4211 7.66667 29.7083 8.95386 29.7083 10.5417V38.4606C36.7412 39.3979 42.1667 45.4193 42.1667 52.7083V58.4583C42.1667 65.7474 36.7412 71.7688 29.7083 72.7061V81.4583C29.7083 83.0461 28.4211 84.3333 26.8333 84.3333C25.2455 84.3333 23.9583 83.0461 23.9583 81.4583V72.7061C16.9254 71.7688 11.5 65.7474 11.5 58.4583V52.7083C11.5 45.4193 16.9254 39.3979 23.9583 38.4606V10.5417C23.9583 8.95386 25.2455 7.66667 26.8333 7.66667Z" fill="black"/>
<path d="M65.1667 7.66667C66.7544 7.66667 68.0416 8.95388 68.0417 10.5417V19.2939C75.0746 20.231 80.5 26.2525 80.5 33.5417V58.4583C80.5 65.7474 75.0746 71.7688 68.0417 72.7061V81.4583C68.0417 83.0461 66.7544 84.3333 65.1667 84.3333C63.5789 84.3333 62.2917 83.0461 62.2917 81.4583V72.7061C55.2587 71.7688 49.8333 65.7474 49.8333 58.4583V33.5417C49.8333 26.2525 55.2587 20.231 62.2917 19.2939V10.5417C62.2917 8.95388 63.5789 7.66667 65.1667 7.66667Z" fill="black"/>
</g>
</g>
</svg>

</g>
</g>
</svg>