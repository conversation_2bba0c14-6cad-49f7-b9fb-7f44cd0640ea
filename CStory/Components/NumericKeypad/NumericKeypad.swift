//
//  NumericKeypad.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//  Refactored by <PERSON> on 2025/7/22.
//

import SwiftUI

/// 数字键盘组件
///
/// 基于MVVM架构的通用数字输入键盘，支持多种输入模式和功能配置。
/// 提供标准数字输入、表达式计算、交易金额录入等功能。
///
/// 该组件通过NumericKeypadVM管理不同的键盘配置和输入逻辑，
/// 支持小数位数控制、负数输入、操作按钮自定义等高级功能。
///
/// ## 主要功能
/// - 数字和小数点输入
/// - 表达式计算支持
/// - 删除和清空操作
/// - 自定义操作按钮
/// - 输入验证和格式化
///
/// ## 使用示例
/// ```swift
/// // 基本数字输入
/// NumericKeypad(
///   expression: $amount,
///   transactionAmount: $finalAmount,
///   onSave: { saveAmount() },
///   allowNegative: false,
///   maxDecimalPlaces: 2
/// )
///
/// // 带录音功能的交易键盘
/// NumericKeypad(
///   expression: $expression,
///   transactionAmount: $amount,
///   discountAmount: $discount,
///   onSave: { processTransaction() },
///   onRecord: { startRecording() }
/// )
/// ```
///
/// - Author: NZUE, Claude
/// - Since: 2025.4.11
/// - Version: 2.0 (重构版本)
/// - Note: 支持触觉反馈和动画效果
struct NumericKeypad: View {

  // MARK: - Properties

  /// 数字键盘视图模型
  ///
  /// 管理键盘状态、配置和交互逻辑的视图模型实例。
  /// 包含键盘布局、输入处理、计算逻辑等所有业务功能。
  /// 视图通过观察此对象的变化来自动更新UI。
  @ObservedObject var viewModel: NumericKeypadVM

  // MARK: - Initialization

  /// 使用视图模型初始化数字键盘
  ///
  /// 创建一个由指定视图模型管理的数字键盘实例。
  /// 所有键盘行为和配置都通过传入的视图模型控制。
  ///
  /// - Parameter viewModel: 预配置的NumericKeypadVM实例
  /// - Note: 视图模型应在传入前完成所有必要的配置
  init(viewModel: NumericKeypadVM) {
    self.viewModel = viewModel
  }

  // MARK: - Body

  var body: some View {
    let keyboardContent = VStack(spacing: viewModel.configuration.verticalSpacing) {
      // 内置显示器（仅标准模式）
      if viewModel.showBuiltinDisplay {
        displayView
      }

      // 键盘按钮区域
      keyboardView
    }

    // 根据配置决定是否添加容器边距
    if viewModel.configuration.addContainerPadding {
      keyboardContent.padding(12)
    } else {
      keyboardContent
    }
  }

  // MARK: - Private Views

  /// 内置数字显示器视图
  ///
  /// 显示当前输入表达式和计算结果的内置显示屏。
  /// 采用等宽字体确保数字对齐，支持自动缩放以适应长表达式。
  ///
  /// - Returns: 配置好的显示器视图
  /// - Note: 仅在标准模式下显示，其他模式使用外部显示器
  private var displayView: some View {
    HStack {
      Spacer()
      Text(viewModel.currentText.isEmpty ? "0" : viewModel.currentText)
        .font(.system(size: 32, weight: .medium, design: .monospaced))
        .foregroundColor(.cBlack)
        .multilineTextAlignment(.trailing)
        .lineLimit(1)
        .minimumScaleFactor(0.5)
        .truncationMode(.head)
    }
    .frame(minWidth: 0, maxWidth: .infinity, minHeight: 42)
    .padding(.horizontal, 16)
    .padding(.vertical, 10)
    .background(.cWhite.opacity(0.5))
    .cornerRadius(24)
    .overlay(
      RoundedRectangle(cornerRadius: 24)
        .strokeBorder(.cAccentBlue.opacity(0.08), lineWidth: 1)
    )
  }

  /// 键盘按钮网格视图
  ///
  /// 根据视图模型配置动态生成的键盘按钮布局。
  /// 支持多种键盘模式和按钮类型，包括数字、操作符、功能按钮等。
  ///
  /// - Returns: 完整的键盘按钮网格视图
  /// - Note: 布局和按钮行为完全由视图模型控制
  private var keyboardView: some View {
    ForEach(viewModel.buttonLayout.indices, id: \.self) { rowIndex in
      let row = viewModel.buttonLayout[rowIndex]

      if isLastRow(rowIndex) && viewModel.style == .standard {
        // 标准模式最后一行：特殊布局（宽版0按钮）
        standardLastRowView(row)
      } else {
        // 普通行：等宽按钮
        normalRowView(row)
      }
    }
  }

  /// 普通行视图
  private func normalRowView(_ row: [NumericKeypadButton]) -> some View {
    HStack(spacing: 8) {
      ForEach(row, id: \.self) { button in
        keypadButton(button)
      }
    }
  }

  /// 标准模式最后一行视图（宽版0按钮）
  private func standardLastRowView(_ row: [NumericKeypadButton]) -> some View {
    GeometryReader { geometry in
      let buttonWidth = (geometry.size.width - 24) / 4
      HStack(spacing: 8) {
        // 宽版0按钮
        if let zeroButton = row.first(where: {
          if case .number("0") = $0 { return true }
          return false
        }) {
          Button(action: {
            viewModel.handleButtonTap(zeroButton)
          }) {
            Text("0")
              .font(zeroButton.font)
              .foregroundColor(.cBlack)
              .frame(width: buttonWidth * 2 + 8, height: 44)
              .background(zeroButton.backgroundColor(for: viewModel.style))
              .cornerRadius(12)
              .overlay(
                RoundedRectangle(cornerRadius: 12)
                  .strokeBorder(.cAccentBlue.opacity(0.08), lineWidth: 1)
              )
          }
          .simultaneousGesture(
            LongPressGesture(minimumDuration: 0.5).onEnded { _ in
              if zeroButton == .delete {
                viewModel.handleLongPressDelete()
              }
            }
          )
        }

        // 其他按钮
        ForEach(
          row.filter({
            if case .number("0") = $0 { return false }
            return true
          }), id: \.self
        ) { button in
          keypadButton(button, width: buttonWidth)
        }
      }
    }
    .frame(height: 44)
  }

  /// 通用按钮视图
  private func keypadButton(
    _ button: NumericKeypadButton,
    width: CGFloat? = nil
  ) -> some View {
    Button(action: {
      viewModel.handleButtonTap(button)
    }) {
      Text(buttonText(for: button))
        .font(button.font)
        .foregroundColor(buttonForegroundColor(for: button))
        .frame(width: width, height: 44)
        .frame(maxWidth: width == nil ? .infinity : nil)
        .background(button.backgroundColor(for: viewModel.style))
        .cornerRadius(buttonCornerRadius)
        .overlay(
          RoundedRectangle(cornerRadius: buttonCornerRadius)
            .strokeBorder(strokeColor, lineWidth: 1)
        )
    }
    .simultaneousGesture(
      LongPressGesture(minimumDuration: 0.5).onEnded { _ in
        if button == .delete {
          viewModel.handleLongPressDelete()
        }
      }
    )
  }

  // MARK: - Helper Methods

  /// 获取按钮文本
  private func buttonText(for button: NumericKeypadButton) -> String {
    if button == .save {
      // 监听按钮文本更新触发器以确保及时更新
      _ = viewModel.buttonTextUpdateTrigger
      return viewModel.dynamicSaveButtonText()
    }
    return button.text
  }

  /// 获取按钮前景色
  private func buttonForegroundColor(for button: NumericKeypadButton) -> Color {
    return viewModel.style == .standard ? .cBlack : .cBlack.opacity(0.8)
  }

  /// 按钮圆角半径
  private var buttonCornerRadius: CGFloat {
    return viewModel.style == .standard ? 12 : 21
  }

  /// 边框颜色
  private var strokeColor: Color {
    return .cAccentBlue.opacity(0.08)
  }

  /// 判断是否为最后一行
  private func isLastRow(_ index: Int) -> Bool {
    return index == viewModel.buttonLayout.count - 1
  }
}

// MARK: - Convenience Initializers

extension NumericKeypad {

  /// 创建标准数字键盘（向后兼容）
  init(
    text: Binding<String>,
    onSave: (() -> Void)? = nil,
    allowNegative: Bool = false,
    maxDecimalPlaces: Int = 2
  ) {
    let vm = NumericKeypadVM.standard(
      text: text,
      allowNegative: allowNegative,
      maxDecimalPlaces: maxDecimalPlaces,
      onSave: onSave
    )
    self.viewModel = vm
  }

  /// 创建交易数字键盘（向后兼容）
  init(
    expression: Binding<String>,
    transactionAmount: Binding<String>,
    discountAmount: Binding<String>,
    onSave: (() -> Void)? = nil,
    onRecord: (() -> Void)? = nil,
    allowNegative: Bool = false,
    maxDecimalPlaces: Int = 2
  ) {
    let vm = NumericKeypadVM.transaction(
      expression: expression,
      transactionAmount: transactionAmount,
      discountAmount: discountAmount,
      allowNegative: allowNegative,
      maxDecimalPlaces: maxDecimalPlaces,
      onSave: onSave,
      onRecord: onRecord
    )
    self.viewModel = vm
  }
}

// MARK: - Preview

#Preview("标准模式") {
  @Previewable @State var text = "123.45"

  VStack {
    Spacer()

    NumericKeypad(
      text: $text,
      onSave: {
        print("保存金额: \(text)")
      }
    )
    .background(Color.gray.opacity(0.05))
    .cornerRadius(24)
    .padding()
  }
  .background(Color.gray.opacity(0.1) as Color)
}

#Preview("交易模式") {
  @Previewable @State var expression = "0"
  @Previewable @State var amount = "0"
  @Previewable @State var discount = "0"

  VStack {
    // 显示当前表达式
    Text(expression)
      .font(.system(size: 32, weight: .medium))
      .padding()

    NumericKeypad(
      expression: $expression,
      transactionAmount: $amount,
      discountAmount: $discount,
      onSave: {
        print("保存: \(expression)")
      },
      onRecord: {
        print("再记: \(expression)")
        expression = "0"
      }
    )
    .padding()
  }
  .background(Color.gray.opacity(0.1) as Color)
}

#Preview("ViewModel方式") {
  @Previewable @State var text = "456.78"

  let vm = NumericKeypadVM.standard(
    text: Binding(get: { text }, set: { text = $0 }),
    allowNegative: true,
    maxDecimalPlaces: 3,
    onSave: {
      print("ViewModel保存: \(text)")
    }
  )

  VStack {
    NumericKeypad(viewModel: vm)
      .padding()
  }
  .background(Color.gray.opacity(0.1) as Color)
}
