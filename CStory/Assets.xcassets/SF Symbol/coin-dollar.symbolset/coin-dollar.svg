<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg
PUBLIC "-//W3C//DTD SVG 1.1//EN"
     "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
     <!--Created with SF Symbol Generator (v1.0.0)-->

<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="3300" height="2200">
<!--glyph: "coin-dollar.medium", point size: 100.0-->
<style>.SFSymbolsPreviewWireframe {fill:none;opacity:1.0;stroke:black;stroke-width:0.5}
</style>
<g id="Notes">
<rect height="2200" id="artboard" style="fill:white;opacity:1" width="3300" x="0" y="0"/>
<line style="fill:none;stroke:black;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="292" y2="292"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 263 322)">Weight/Scale Variations</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 559.711 322)">Ultralight</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 856.422 322)">Thin</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1153.13 322)">Light</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1449.84 322)">Regular</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1746.56 322)">Medium</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2043.27 322)">Semibold</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2339.98 322)">Bold</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2636.69 322)">Heavy</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2933.4 322)">Black</text>
<line style="fill:none;stroke:black;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1903" y2="1903"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 263 1953)">Design Variations</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1971)">Symbols are supported in up to nine weights and three scales.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1989)">For optimal layout with text and other symbols, vertically align</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 2007)">symbols with the adjacent text.</text>
<line style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="776" x2="776" y1="1919" y2="1933"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 776 1953)">Margins</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 1971)">Leading and trailing margins on the left and right side of each symbol</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 1989)">can be adjusted by modifying the x-location of the margin guidelines.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 2007)">Modifications are automatically applied proportionally to all</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 2025)">scales and weights.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 1289 1953)">Exporting</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 1289 1971)">Symbols should be outlined when exporting to ensure the</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 1289 1989)">design is preserved when submitting to Xcode.</text>
<text id="template-version" style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1933)">Template v.5.0</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1951)">Requires Xcode 15 or greater</text>
<text id="descriptive-name" style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1969)">Generated from coin-dollar</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1987)">Typeset at 100.0 points</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 726)">Small</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1156)">Medium</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1586)">Large</text>
</g>
<g id="Guides">
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 696)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-S" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="696" y2="696"/>
<line id="Capline-S" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="625.541" y2="625.541"/>
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 1126)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-M" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1126" y2="1126"/>
<line id="Capline-M" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1055.54" y2="1055.54"/>
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 1556)">
 <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-L" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1556" y2="1556"/>
<line id="Capline-L" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1485.54" y2="1485.54"/>
<line id="left-margin-Ultralight-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="513.711" x2="513.711" y1="600.785" y2="720.121"/>
<line id="right-margin-Ultralight-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="605.711" x2="605.711" y1="600.785" y2="720.121"/>
<line id="left-margin-Regular-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="1403.84" x2="1403.84" y1="600.785" y2="720.121"/>
<line id="right-margin-Regular-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="1495.84" x2="1495.84" y1="600.785" y2="720.121"/>
<line id="left-margin-Black-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="2887.4" x2="2887.4" y1="600.785" y2="720.121"/>
<line id="right-margin-Black-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="2979.4" x2="2979.4" y1="600.785" y2="720.121"/>
</g>
<g id="Symbols">
<g id="Ultralight-S" transform="matrix(1 0 0 1 513.711 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="coin-dollar">
<g id="Union">
<path d="M46 21.4577C47.5878 21.4577 48.875 22.7449 48.875 24.3327V27.6007C49.6629 27.7564 50.4347 27.9615 51.1735 28.2259C53.4486 29.0406 55.701 30.4779 57.0133 32.7331C57.8116 34.1055 57.3449 35.8653 55.9727 36.6637C54.6005 37.4615 52.8404 36.9985 52.042 35.6268C51.6354 34.9283 50.7301 34.1734 49.2381 33.639C47.7771 33.1159 46.0148 32.9048 44.3304 33.07C42.6287 33.237 41.2312 33.7622 40.3286 34.4813C39.5123 35.1322 39.0224 35.9846 39.0221 37.244C39.0222 38.7746 39.628 39.7031 40.8639 40.5607C42.3053 41.5605 44.3378 42.2616 46.9097 43.1213C49.2616 43.9074 52.1562 44.8534 54.4079 46.4155C56.8643 48.1203 58.7198 50.6724 58.7204 54.4528C58.7204 57.6321 57.2688 60.1261 55.1154 61.805C53.3228 63.202 51.1014 64.0003 48.875 64.3543V67.6711C48.8737 69.2578 47.587 70.5461 46 70.5461C44.413 70.5461 43.1263 69.2578 43.125 67.6711V64.3506C42.0933 64.1893 41.0811 63.9446 40.119 63.6094C37.7281 62.7759 35.3806 61.3057 33.9647 59.0049C33.1337 57.6528 33.5562 55.8836 34.908 55.0518C36.2597 54.2207 38.0288 54.6407 38.8612 55.9914C39.3615 56.8044 40.4019 57.6198 42.0132 58.1813C43.5962 58.7328 45.4845 58.9476 47.284 58.769C49.103 58.5885 50.6054 58.0289 51.5815 57.2679C52.4719 56.5734 52.9704 55.695 52.9704 54.4528C52.9699 52.9229 52.3642 51.9972 51.1286 51.1398C49.6874 50.14 47.6582 49.4351 45.0866 48.5755C42.7343 47.7893 39.8367 46.8475 37.5846 45.285C35.1277 43.58 33.2722 41.0254 33.2721 37.244C33.2724 34.1204 34.6496 31.6541 36.7423 29.9854C38.5744 28.5247 40.8661 27.7392 43.125 27.4285V24.3327C43.125 22.7449 44.4122 21.4577 46 21.4577Z" fill="black"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M46 7.66667C67.1708 7.66667 84.3333 24.8291 84.3333 46C84.3333 67.1708 67.1708 84.3333 46 84.3333C24.8291 84.3333 7.66667 67.1708 7.66667 46C7.66667 24.8291 24.8291 7.66667 46 7.66667ZM46 13.4167C28.0047 13.4167 13.4167 28.0047 13.4167 46C13.4167 63.9952 28.0047 78.5833 46 78.5833C63.9951 78.5833 78.5833 63.9951 78.5833 46C78.5833 28.0047 63.9952 13.4167 46 13.4167Z" fill="black"/>
</g>
</g>
</svg>

</g>
<g id="Regular-S" transform="matrix(1 0 0 1 1403.84 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="coin-dollar">
<g id="Union">
<path d="M46 21.4577C47.5878 21.4577 48.875 22.7449 48.875 24.3327V27.6007C49.6629 27.7564 50.4347 27.9615 51.1735 28.2259C53.4486 29.0406 55.701 30.4779 57.0133 32.7331C57.8116 34.1055 57.3449 35.8653 55.9727 36.6637C54.6005 37.4615 52.8404 36.9985 52.042 35.6268C51.6354 34.9283 50.7301 34.1734 49.2381 33.639C47.7771 33.1159 46.0148 32.9048 44.3304 33.07C42.6287 33.237 41.2312 33.7622 40.3286 34.4813C39.5123 35.1322 39.0224 35.9846 39.0221 37.244C39.0222 38.7746 39.628 39.7031 40.8639 40.5607C42.3053 41.5605 44.3378 42.2616 46.9097 43.1213C49.2616 43.9074 52.1562 44.8534 54.4079 46.4155C56.8643 48.1203 58.7198 50.6724 58.7204 54.4528C58.7204 57.6321 57.2688 60.1261 55.1154 61.805C53.3228 63.202 51.1014 64.0003 48.875 64.3543V67.6711C48.8737 69.2578 47.587 70.5461 46 70.5461C44.413 70.5461 43.1263 69.2578 43.125 67.6711V64.3506C42.0933 64.1893 41.0811 63.9446 40.119 63.6094C37.7281 62.7759 35.3806 61.3057 33.9647 59.0049C33.1337 57.6528 33.5562 55.8836 34.908 55.0518C36.2597 54.2207 38.0288 54.6407 38.8612 55.9914C39.3615 56.8044 40.4019 57.6198 42.0132 58.1813C43.5962 58.7328 45.4845 58.9476 47.284 58.769C49.103 58.5885 50.6054 58.0289 51.5815 57.2679C52.4719 56.5734 52.9704 55.695 52.9704 54.4528C52.9699 52.9229 52.3642 51.9972 51.1286 51.1398C49.6874 50.14 47.6582 49.4351 45.0866 48.5755C42.7343 47.7893 39.8367 46.8475 37.5846 45.285C35.1277 43.58 33.2722 41.0254 33.2721 37.244C33.2724 34.1204 34.6496 31.6541 36.7423 29.9854C38.5744 28.5247 40.8661 27.7392 43.125 27.4285V24.3327C43.125 22.7449 44.4122 21.4577 46 21.4577Z" fill="black"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M46 7.66667C67.1708 7.66667 84.3333 24.8291 84.3333 46C84.3333 67.1708 67.1708 84.3333 46 84.3333C24.8291 84.3333 7.66667 67.1708 7.66667 46C7.66667 24.8291 24.8291 7.66667 46 7.66667ZM46 13.4167C28.0047 13.4167 13.4167 28.0047 13.4167 46C13.4167 63.9952 28.0047 78.5833 46 78.5833C63.9951 78.5833 78.5833 63.9951 78.5833 46C78.5833 28.0047 63.9952 13.4167 46 13.4167Z" fill="black"/>
</g>
</g>
</svg>

</g>
<g id="Black-S" transform="matrix(1 0 0 1 2887.4 614.7705)">
<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="coin-dollar">
<g id="Union">
<path d="M46 21.4577C47.5878 21.4577 48.875 22.7449 48.875 24.3327V27.6007C49.6629 27.7564 50.4347 27.9615 51.1735 28.2259C53.4486 29.0406 55.701 30.4779 57.0133 32.7331C57.8116 34.1055 57.3449 35.8653 55.9727 36.6637C54.6005 37.4615 52.8404 36.9985 52.042 35.6268C51.6354 34.9283 50.7301 34.1734 49.2381 33.639C47.7771 33.1159 46.0148 32.9048 44.3304 33.07C42.6287 33.237 41.2312 33.7622 40.3286 34.4813C39.5123 35.1322 39.0224 35.9846 39.0221 37.244C39.0222 38.7746 39.628 39.7031 40.8639 40.5607C42.3053 41.5605 44.3378 42.2616 46.9097 43.1213C49.2616 43.9074 52.1562 44.8534 54.4079 46.4155C56.8643 48.1203 58.7198 50.6724 58.7204 54.4528C58.7204 57.6321 57.2688 60.1261 55.1154 61.805C53.3228 63.202 51.1014 64.0003 48.875 64.3543V67.6711C48.8737 69.2578 47.587 70.5461 46 70.5461C44.413 70.5461 43.1263 69.2578 43.125 67.6711V64.3506C42.0933 64.1893 41.0811 63.9446 40.119 63.6094C37.7281 62.7759 35.3806 61.3057 33.9647 59.0049C33.1337 57.6528 33.5562 55.8836 34.908 55.0518C36.2597 54.2207 38.0288 54.6407 38.8612 55.9914C39.3615 56.8044 40.4019 57.6198 42.0132 58.1813C43.5962 58.7328 45.4845 58.9476 47.284 58.769C49.103 58.5885 50.6054 58.0289 51.5815 57.2679C52.4719 56.5734 52.9704 55.695 52.9704 54.4528C52.9699 52.9229 52.3642 51.9972 51.1286 51.1398C49.6874 50.14 47.6582 49.4351 45.0866 48.5755C42.7343 47.7893 39.8367 46.8475 37.5846 45.285C35.1277 43.58 33.2722 41.0254 33.2721 37.244C33.2724 34.1204 34.6496 31.6541 36.7423 29.9854C38.5744 28.5247 40.8661 27.7392 43.125 27.4285V24.3327C43.125 22.7449 44.4122 21.4577 46 21.4577Z" fill="black"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M46 7.66667C67.1708 7.66667 84.3333 24.8291 84.3333 46C84.3333 67.1708 67.1708 84.3333 46 84.3333C24.8291 84.3333 7.66667 67.1708 7.66667 46C7.66667 24.8291 24.8291 7.66667 46 7.66667ZM46 13.4167C28.0047 13.4167 13.4167 28.0047 13.4167 46C13.4167 63.9952 28.0047 78.5833 46 78.5833C63.9951 78.5833 78.5833 63.9951 78.5833 46C78.5833 28.0047 63.9952 13.4167 46 13.4167Z" fill="black"/>
</g>
</g>
</svg>

</g>
</g>
</svg>